'use client'

import { <PERSON><PERSON>, <PERSON>gin<PERSON> } from '@ads/components-react'
import { Plus } from 'lucide-react'
import { useRouter } from 'next/navigation'
import { useState } from 'react'
import { useQuery } from 'react-query'
import { useStore } from 'zustand'

import { Filter } from '@/components/filter/filter'
import { PageLayout } from '@/components/layouts/page-layout'
import { CardsSkeleton } from '@/components/loaders/skeletons/cards'
import { useAuth } from '@/contexts/AuthContext'
import { getStudyPlans } from '@/http/study-plan'
import { StudyPlanData } from '@/model/study-plan'
import { useFilterStore } from '@/store/useFilterStore'

import { CardInfo } from './components/card'
import { EmptyState } from './components/empty-state'

export default function Trilhas() {
  const router = useRouter()
  const { user } = useAuth()
  const { search, status } = useStore(useFilterStore)
  const { onChangeSearch, onChangeStatus } = useFilterStore()
  const [currentPage, setCurrentPage] = useState(1)

  const handleStatus = (): boolean | null => {
    if (status === 'INACTIVE') return false
    if (status === 'FINISHED') return true
    if (status === 'ACTIVE') return true

    return true
  }

  const {
    data: studyPlans,
    isLoading,
    isFetching,
  } = useQuery({
    queryKey: ['getStudyPlansGql', currentPage, user, search, status],
    queryFn: () =>
      getStudyPlans({
        page: currentPage,
        company_id: user?.metadata.company_id ?? 0,
        limit: 10,
        name: search ?? '',
        onlyPDI: false,
        user_id: Number(user?.id),
        end_date: status === 'FINISHED' ? new Date() : null,
        status: handleStatus(),
      }),
    refetchOnWindowFocus: false,
  })

  return (
    <PageLayout
      title="Trilhas de aprendizagem"
      description="Gerencie, acompanhe e analise o progresso de desenvolvimento dos colaboradores em tempo real."
      actionButton={
        <Button
          trailingIcon={Plus}
          onClick={() => router.push('/trilhas-de-aprendizagem/criar')}
          className="w-full sm:w-fit"
        >
          Nova Trilha
        </Button>
      }
    >
      <div className="space-y-8 rounded-md bg-ctx-layout-body px-6 py-10">
        <div className="mb-8 flex items-center justify-between gap-4">
          <Filter
            pageKey="trilhas"
            placeholder="Busca por Trilha"
            onChangeStatus={onChangeStatus}
            onChangeSearch={onChangeSearch}
          />
        </div>
        <div className="flex flex-wrap gap-8">
          {isLoading || isFetching ? (
            <CardsSkeleton />
          ) : studyPlans?.studyPlans.data.length === 0 ? (
            <EmptyState />
          ) : (
            studyPlans?.studyPlans.data.map((studyPlan) => (
              <CardInfo
                key={studyPlan.id}
                studyPlanDataInfo={studyPlan as StudyPlanData}
              />
            ))
          )}
        </div>
        <div className="flex justify-center">
          {(studyPlans?.studyPlans.data.length as number) > 0 && (
            <Pagination
              controlText={{ previous: 'Anterior', next: 'Próximo' }}
              activeIndex={currentPage - 1}
              setActiveIndex={(e) => setCurrentPage((e as number) + 1)}
              pageAmount={Math.ceil(
                (studyPlans?.studyPlans?.total as number) /
                  (studyPlans?.studyPlans?.perPage as number)
              )}
              ellipsisLabel="Buscar mais itens"
            />
          )}
        </div>
      </div>
    </PageLayout>
  )
}
