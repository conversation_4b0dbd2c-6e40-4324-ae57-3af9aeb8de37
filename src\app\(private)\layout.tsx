import { Header } from '@/components/layouts/header/header'
import { AppSidebar } from '@/components/layouts/sidebar'
import { ScrollArea } from '@/components/ui/scroll-area'
import { SidebarProvider } from '@/components/ui/sidebar'

type RootLayoutProps = {
  children: React.ReactNode
}

export default function RootLayout({ children }: RootLayoutProps) {
  return (
    <SidebarProvider>
      <div className="w-screen">
        <Header />
        <div className="flex min-h-full">
          <AppSidebar />
          <ScrollArea className="max-h-[calc(100vh-4rem)] w-full">
            <div className="h-full min-h-[calc(100vh-4rem)] bg-ctx-layout-surface md:rounded-tl-[32px]">
              {children}
            </div>
          </ScrollArea>
        </div>
      </div>
    </SidebarProvider>
  )
}
