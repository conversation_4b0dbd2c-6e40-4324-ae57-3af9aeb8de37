/* eslint-disable */
import { TypedDocumentNode as DocumentNode } from '@graphql-typed-document-node/core';
export type Maybe<T> = T | null;
export type InputMaybe<T> = Maybe<T>;
export type Exact<T extends { [key: string]: unknown }> = { [K in keyof T]: T[K] };
export type MakeOptional<T, K extends keyof T> = Omit<T, K> & { [SubKey in K]?: Maybe<T[SubKey]> };
export type MakeMaybe<T, K extends keyof T> = Omit<T, K> & { [SubKey in K]: Maybe<T[SubKey]> };
export type MakeEmpty<T extends { [key: string]: unknown }, K extends keyof T> = { [_ in K]?: never };
export type Incremental<T> = T | { [P in keyof T]?: P extends ' $fragmentName' | '__typename' ? T[P] : never };
/** All built-in and custom scalars, mapped to their actual values */
export type Scalars = {
  ID: { input: string; output: string; }
  String: { input: string; output: string; }
  Boolean: { input: boolean; output: boolean; }
  Int: { input: number; output: number; }
  Float: { input: number; output: number; }
  /** AvailabilityFilter custom scalar type */
  AvailabilityFilter: { input: any; output: any; }
  /** A date-time string at UTC, such as 2019-12-03T09:54:33Z, compliant with the date-time format. */
  DateTime: { input: any; output: any; }
};

export type ActivateEnrollmentInput = {
  enrollment_id: Scalars['Int']['input'];
  user_id: Scalars['Int']['input'];
};

export type B2bStatisticsGraphData = {
  date_of_view: Scalars['DateTime']['output'];
  value: Scalars['Float']['output'];
};

export type B2bStatisticsGraphs = {
  activitiesCompleted: Array<B2bStatisticsGraphData>;
  coursesCompleted: Array<B2bStatisticsGraphData>;
};


export type B2bStatisticsGraphsActivitiesCompletedArgs = {
  company_id: InputMaybe<Scalars['Int']['input']>;
  end_date: InputMaybe<Scalars['DateTime']['input']>;
  limit: InputMaybe<Scalars['Int']['input']>;
  start_date: InputMaybe<Scalars['DateTime']['input']>;
};


export type B2bStatisticsGraphsCoursesCompletedArgs = {
  company_id: InputMaybe<Scalars['Int']['input']>;
  end_date: InputMaybe<Scalars['DateTime']['input']>;
  limit: InputMaybe<Scalars['Int']['input']>;
  start_date: InputMaybe<Scalars['DateTime']['input']>;
};

export type B2bStatisticsModel = {
  completedCoursesCount: StatisticsData;
  engagedUsersCount: StatisticsData;
  graphs: B2bStatisticsGraphs;
  mostWatchedCoursesRanking: Array<B2bStatisticsMostWatchedCoursesRanking>;
  usersCount: StatisticsData;
  usersCourseCompletedRanking: Array<UserEngagementRankingDto>;
  usersEngagementRanking: Array<UserEngagementRankingDto>;
};


export type B2bStatisticsModelCompletedCoursesCountArgs = {
  company_id: InputMaybe<Scalars['Int']['input']>;
  end_date: InputMaybe<Scalars['DateTime']['input']>;
  limit: InputMaybe<Scalars['Int']['input']>;
  start_date: InputMaybe<Scalars['DateTime']['input']>;
};


export type B2bStatisticsModelEngagedUsersCountArgs = {
  company_id: InputMaybe<Scalars['Int']['input']>;
  end_date: InputMaybe<Scalars['DateTime']['input']>;
  limit: InputMaybe<Scalars['Int']['input']>;
  start_date: InputMaybe<Scalars['DateTime']['input']>;
};


export type B2bStatisticsModelMostWatchedCoursesRankingArgs = {
  company_id: InputMaybe<Scalars['Int']['input']>;
  end_date: InputMaybe<Scalars['DateTime']['input']>;
  limit: InputMaybe<Scalars['Int']['input']>;
  start_date: InputMaybe<Scalars['DateTime']['input']>;
};


export type B2bStatisticsModelUsersCountArgs = {
  company_id: InputMaybe<Scalars['Int']['input']>;
  end_date: InputMaybe<Scalars['DateTime']['input']>;
  limit: InputMaybe<Scalars['Int']['input']>;
  start_date: InputMaybe<Scalars['DateTime']['input']>;
};


export type B2bStatisticsModelUsersCourseCompletedRankingArgs = {
  company_id: InputMaybe<Scalars['Int']['input']>;
  end_date: InputMaybe<Scalars['DateTime']['input']>;
  limit: InputMaybe<Scalars['Int']['input']>;
  start_date: InputMaybe<Scalars['DateTime']['input']>;
};


export type B2bStatisticsModelUsersEngagementRankingArgs = {
  company_id: InputMaybe<Scalars['Int']['input']>;
  end_date: InputMaybe<Scalars['DateTime']['input']>;
  limit: InputMaybe<Scalars['Int']['input']>;
  start_date: InputMaybe<Scalars['DateTime']['input']>;
};

export type B2bStatisticsMostWatchedCoursesRanking = {
  course: CourseEntity;
  views: Scalars['Float']['output'];
};

export type CertificationSettings = {
  height: Scalars['Float']['output'];
  left: Scalars['Float']['output'];
  text: Scalars['String']['output'];
  top: Scalars['Float']['output'];
  width: Scalars['Float']['output'];
};

export type ChaptersPivot = {
  id: Scalars['Float']['input'];
  order: Scalars['Float']['input'];
};

export type CityEntity = {
  created_at: Scalars['DateTime']['output'];
  id: Scalars['Float']['output'];
  name: Scalars['String']['output'];
  state: StateEntity;
  state_id: Scalars['Float']['output'];
  updated_at: Scalars['DateTime']['output'];
};

export type CompanyEntity = {
  cnpj: Scalars['String']['output'];
  created_at: Scalars['DateTime']['output'];
  email: Scalars['String']['output'];
  enrollments: Maybe<Array<EnrollmentB2BMetadataEntity>>;
  id: Scalars['ID']['output'];
  name: Scalars['String']['output'];
  phone_number: Scalars['String']['output'];
  slug: Scalars['String']['output'];
  state_registration: Maybe<Scalars['String']['output']>;
  status: Scalars['Boolean']['output'];
  updated_at: Scalars['DateTime']['output'];
};

export type CompanyGroupEntity = {
  company_group_users_pivot: Maybe<Array<CompanyGroupUsersPivotEntity>>;
  company_id: Scalars['Float']['output'];
  created_at: Scalars['DateTime']['output'];
  groupUsers: Maybe<PaginatedCompanyGroupUsersResponse>;
  id: Scalars['Float']['output'];
  name: Scalars['String']['output'];
  updated_at: Scalars['DateTime']['output'];
  users_count: Scalars['Float']['output'];
};


export type CompanyGroupEntityGroupUsersArgs = {
  limit: InputMaybe<Scalars['Int']['input']>;
  page: InputMaybe<Scalars['Int']['input']>;
};

export type CompanyGroupUsersPivotEntity = {
  company_group: Maybe<CompanyGroupEntity>;
  created_at: Scalars['DateTime']['output'];
  group_id: Scalars['Float']['output'];
  user: Maybe<UserEntity>;
  user_id: Scalars['Float']['output'];
};

export type CompanySquadEntity = {
  company: Maybe<CompanyEntity>;
  company_id: Scalars['Float']['output'];
  created_at: Scalars['DateTime']['output'];
  id: Scalars['Float']['output'];
  title: Scalars['String']['output'];
  updated_at: Scalars['DateTime']['output'];
  users: Maybe<PaginatedCompanySquadUsersResponse>;
  users_count: Scalars['Float']['output'];
};


export type CompanySquadEntityUsersArgs = {
  limit: InputMaybe<Scalars['Int']['input']>;
  page: InputMaybe<Scalars['Int']['input']>;
};

export type ContractEntity = {
  content: Scalars['String']['output'];
  contract_type: EContractType;
  created_at: Scalars['DateTime']['output'];
  id: Scalars['Float']['output'];
  title: Scalars['String']['output'];
  updated_at: Scalars['DateTime']['output'];
};

export type CountryEntity = {
  code: Scalars['String']['output'];
  created_at: Scalars['DateTime']['output'];
  id: Scalars['Float']['output'];
  name: Scalars['String']['output'];
  updated_at: Scalars['DateTime']['output'];
};

export type CourseCategoryEntity = {
  courses: Array<Maybe<CourseEntity>>;
  courses_count: Maybe<Scalars['Float']['output']>;
  created_at: Scalars['DateTime']['output'];
  description: Maybe<Scalars['String']['output']>;
  icon: Maybe<FileEntity>;
  icon_id: Maybe<Scalars['Float']['output']>;
  id: Scalars['Float']['output'];
  slug: Scalars['String']['output'];
  status: Scalars['Boolean']['output'];
  title: Scalars['String']['output'];
  updated_at: Scalars['DateTime']['output'];
};


export type CourseCategoryEntityCoursesArgs = {
  context: InputMaybe<ESolutionContext>;
};


export type CourseCategoryEntityCoursesCountArgs = {
  context: InputMaybe<ESolutionContext>;
};

export type CourseCertificationEntity = {
  certification_background: Maybe<FileEntity>;
  certification_background_id: Maybe<Scalars['Float']['output']>;
  certification_settings: Maybe<Array<CertificationSettings>>;
  courses: Maybe<Array<CourseEntity>>;
  created_at: Scalars['DateTime']['output'];
  id: Scalars['ID']['output'];
  title: Scalars['String']['output'];
  updated_at: Scalars['DateTime']['output'];
};

export type CourseChapterContentEntity = {
  chapter_id: Scalars['Float']['output'];
  content: Scalars['String']['output'];
  created_at: Scalars['DateTime']['output'];
  description: Maybe<Scalars['String']['output']>;
  id: Scalars['ID']['output'];
  order: Scalars['Float']['output'];
  title: Scalars['String']['output'];
  type: ECourseChapterContentType;
  updated_at: Scalars['DateTime']['output'];
};

export type CourseChapterEntity = {
  assets: Maybe<Array<FileEntity>>;
  contents: Maybe<Array<CourseChapterContentEntity>>;
  course_id: Scalars['Float']['output'];
  created_at: Scalars['DateTime']['output'];
  duration: Scalars['Float']['output'];
  id: Scalars['ID']['output'];
  order: Scalars['Float']['output'];
  title: Scalars['String']['output'];
  updated_at: Scalars['DateTime']['output'];
};

export type CourseContentsUpdateInput = {
  contents: Array<Scalars['String']['input']>;
  id: InputMaybe<Scalars['ID']['input']>;
  title: Scalars['String']['input'];
};

export type CourseCtaEntity = {
  created_at: Scalars['DateTime']['output'];
  id: Scalars['ID']['output'];
  title: Scalars['String']['output'];
  updated_at: Scalars['DateTime']['output'];
  url: Scalars['String']['output'];
};

export type CourseEntity = {
  ads_id: Maybe<Scalars['String']['output']>;
  available_at: Maybe<ESolutionContext>;
  card_description: Maybe<Scalars['String']['output']>;
  card_title: Maybe<Scalars['String']['output']>;
  categories: Maybe<Array<CourseCategoryEntity>>;
  certification: Maybe<CourseCertificationEntity>;
  certification_id: Maybe<Scalars['Float']['output']>;
  chapters: Maybe<Array<CourseChapterEntity>>;
  content: Maybe<Scalars['String']['output']>;
  cost_center_id: Maybe<Scalars['String']['output']>;
  course_certification: Maybe<CourseCertificationEntity>;
  course_combo_id: Maybe<Scalars['Float']['output']>;
  course_duration: Scalars['Float']['output'];
  course_type: Maybe<Scalars['String']['output']>;
  cover: Maybe<FileEntity>;
  cover_hover: Maybe<FileEntity>;
  cover_hover_id: Maybe<Scalars['Float']['output']>;
  cover_id: Maybe<Scalars['Float']['output']>;
  created_at: Scalars['DateTime']['output'];
  cta: Maybe<CourseCtaEntity>;
  description: Maybe<Scalars['String']['output']>;
  discount_percent: Maybe<Scalars['Float']['output']>;
  duration: Maybe<Scalars['String']['output']>;
  featured: Maybe<Scalars['Boolean']['output']>;
  features: Maybe<Array<CourseFeatureEntity>>;
  finalization_criteria: Maybe<Array<CourseFinalizationCriteriaEntity>>;
  has_closed_captions: Scalars['Boolean']['output'];
  id: Scalars['ID']['output'];
  is_partner_course: Scalars['Boolean']['output'];
  live: Maybe<Scalars['Boolean']['output']>;
  lms_course_id: Maybe<Scalars['Float']['output']>;
  modality: Maybe<ECourseModality>;
  new: Maybe<Scalars['Boolean']['output']>;
  partner: Maybe<PartnerEntity>;
  partnership: Maybe<Scalars['Boolean']['output']>;
  payment_installments: Maybe<Scalars['Float']['output']>;
  pre_launch: Maybe<Scalars['Boolean']['output']>;
  price: Maybe<Scalars['Float']['output']>;
  promo_code: Maybe<Scalars['String']['output']>;
  published_at: Maybe<Scalars['DateTime']['output']>;
  related_courses: Maybe<Array<CourseEntity>>;
  slug: Scalars['String']['output'];
  start: Maybe<Scalars['String']['output']>;
  status: Scalars['Boolean']['output'];
  study_plan_pivot: Maybe<Array<StudyPlanCoursePivotEntity>>;
  teachers: Array<Maybe<TeacherEntity>>;
  title: Scalars['String']['output'];
  type: Scalars['String']['output'];
  updated_at: Scalars['DateTime']['output'];
  url_video: Maybe<Scalars['String']['output']>;
  user_progress: GetCourseProgressResponse;
  usersCourseProgress: Maybe<Array<UserCourseProgressEntity>>;
  view_course_activities: ViewCourseActivity;
};

export type CourseFeatureEntity = {
  created_at: Scalars['DateTime']['output'];
  id: Scalars['ID']['output'];
  title: Scalars['String']['output'];
  updated_at: Scalars['DateTime']['output'];
};

export type CourseFeatureInput = {
  id: InputMaybe<Scalars['Int']['input']>;
  title: Scalars['String']['input'];
};

export type CourseFeedbackEntity = {
  course: Maybe<CourseEntity>;
  course_id: Scalars['Float']['output'];
  feedback: Maybe<Scalars['String']['output']>;
  share_rating: Scalars['Float']['output'];
  teacher_rating: Scalars['Float']['output'];
  user: Maybe<UserEntity>;
  user_id: Scalars['Float']['output'];
};

export type CourseFinalizationCriteriaEntity = {
  course: Maybe<CourseEntity>;
  course_id: Scalars['Float']['output'];
  created_at: Scalars['DateTime']['output'];
  criteria: ECourseFinalizationCriteria;
  id: Scalars['Float']['output'];
  value: Scalars['Float']['output'];
};

export type CourseFinalizationCriteriaValidator = {
  criteria: ECourseFinalizationCriteria;
  value: Scalars['Float']['input'];
};

export type CourseSectionEntity = {
  courses_pivot: Array<CourseSectionPivotEntity>;
  id: Scalars['Float']['output'];
  is_masterclass: Scalars['Boolean']['output'];
  is_ranked: Scalars['Boolean']['output'];
  order: Scalars['Float']['output'];
  section: SectionEntity;
};

export type CourseSectionPivotEntity = {
  course: CourseEntity;
  course_id: Scalars['Float']['output'];
  course_section_id: Scalars['Float']['output'];
  order: Scalars['Float']['output'];
};

export type CreateCtaInput = {
  title: Scalars['String']['input'];
  url: Scalars['String']['input'];
};

export type CreateChapterContentsInput = {
  content: Scalars['String']['input'];
  description: InputMaybe<Scalars['String']['input']>;
  order: Scalars['Float']['input'];
  title: Scalars['String']['input'];
  type: ECourseChapterContentType;
};

export type CreateCompanyGroupInput = {
  name: Scalars['String']['input'];
  user_ids: Array<Scalars['Float']['input']>;
};

export type CreateCompanyInput = {
  cnpj: Scalars['String']['input'];
  email: Scalars['String']['input'];
  name: Scalars['String']['input'];
  phone_number: Scalars['String']['input'];
  state_registration: InputMaybe<Scalars['String']['input']>;
  status: Scalars['Boolean']['input'];
};

export type CreateCompanySquadInput = {
  company_id: Scalars['Float']['input'];
  title: Scalars['String']['input'];
  user_ids: InputMaybe<Array<Scalars['Float']['input']>>;
};

export type CreateContractInput = {
  content: Scalars['String']['input'];
  contract_type: EContractType;
  title: Scalars['String']['input'];
};

export type CreateCourseCategoryInput = {
  cover: InputMaybe<FileInput>;
  description: InputMaybe<Scalars['String']['input']>;
  icon: InputMaybe<FileInput>;
  status: InputMaybe<Scalars['Boolean']['input']>;
  title: Scalars['String']['input'];
};

export type CreateCourseChapterInput = {
  assets: InputMaybe<Array<FileInput>>;
  contents: Array<CreateChapterContentsInput>;
  course_id: Scalars['Float']['input'];
  duration: Scalars['Float']['input'];
  order: Scalars['Float']['input'];
  title: Scalars['String']['input'];
};

export type CreateCourseInput = {
  assets: InputMaybe<Array<FileInput>>;
  available_at: InputMaybe<ESolutionContext>;
  categories: Array<Scalars['Int']['input']>;
  certification_id: InputMaybe<Scalars['Int']['input']>;
  cost_center_id: InputMaybe<Scalars['String']['input']>;
  cover: FileInput;
  cover_hover: FileInput;
  cta: InputMaybe<CreateCtaInput>;
  description: InputMaybe<Scalars['String']['input']>;
  discount_percent: InputMaybe<Scalars['Int']['input']>;
  features: InputMaybe<Array<CourseFeatureInput>>;
  finalization_criteria: Array<CourseFinalizationCriteriaValidator>;
  has_closed_captions: InputMaybe<Scalars['Boolean']['input']>;
  is_partner_course: InputMaybe<Scalars['Boolean']['input']>;
  lms_course_id: InputMaybe<Scalars['Int']['input']>;
  modality: ECourseModality;
  partner_id: InputMaybe<Scalars['Int']['input']>;
  payment_installments: InputMaybe<Scalars['Int']['input']>;
  price: Scalars['Float']['input'];
  promo_code: InputMaybe<Scalars['String']['input']>;
  related_courses: InputMaybe<Array<RelatedCoursesInput>>;
  release_date: InputMaybe<Scalars['String']['input']>;
  start: InputMaybe<Scalars['String']['input']>;
  status: Scalars['Boolean']['input'];
  subtitle: InputMaybe<Scalars['String']['input']>;
  teachers: InputMaybe<Array<Scalars['Int']['input']>>;
  title: Scalars['String']['input'];
  trailer_url: InputMaybe<Scalars['String']['input']>;
};

export type CreateEnrollmentInput = {
  course_ids: InputMaybe<Array<Scalars['Float']['input']>>;
  expiration_date: InputMaybe<Scalars['DateTime']['input']>;
  type: EEnrollmentType;
  user_id: Scalars['Float']['input'];
};

export type CreateExamInput = {
  course_id: Scalars['Float']['input'];
  grade_criteria: EExamGradeCriteria;
  max_attempts: InputMaybe<Scalars['Float']['input']>;
  max_questions: Scalars['Float']['input'];
  name: Scalars['String']['input'];
  passing_grade: Scalars['Float']['input'];
  questions: Array<CreateExamQuestionInput>;
  randomize_questions: EExamRandomizeQuestions;
  teacher_id: Scalars['Float']['input'];
};

export type CreateExamQuestionAnswerInput = {
  content: Scalars['String']['input'];
  is_correct: Scalars['Boolean']['input'];
};

export type CreateExamQuestionInput = {
  answers: Array<CreateExamQuestionAnswerInput>;
  content: Scalars['String']['input'];
};

export type CreatePaymentBenefitInput = {
  discount_code: Scalars['String']['input'];
  discount_percentage: Scalars['Float']['input'];
  payment_plan_slug: Scalars['String']['input'];
};

export type CreatePaymentCouponInput = {
  applicable_course_ids: InputMaybe<Array<Scalars['Float']['input']>>;
  available_for: ECouponAvailability;
  code: Scalars['String']['input'];
  discount_type: EPaymentDiscountType;
  discount_value: Scalars['Float']['input'];
  end_date: InputMaybe<Scalars['DateTime']['input']>;
  is_cumulative: InputMaybe<Scalars['Boolean']['input']>;
  name: Scalars['String']['input'];
  quantity: Scalars['Float']['input'];
  start_date: Scalars['DateTime']['input'];
  status: InputMaybe<Scalars['Boolean']['input']>;
};

export type CreateStudyPlanInput = {
  ai_generated: InputMaybe<Scalars['Boolean']['input']>;
  company_id: Scalars['Float']['input'];
  courses: Array<StudyPlanCoursePivot>;
  description: Scalars['String']['input'];
  end_date: InputMaybe<Scalars['DateTime']['input']>;
  is_for_all_courses: InputMaybe<Scalars['Boolean']['input']>;
  is_for_all_squads: InputMaybe<Scalars['Boolean']['input']>;
  is_for_all_users: InputMaybe<Scalars['Boolean']['input']>;
  is_pdi: InputMaybe<Scalars['Boolean']['input']>;
  name: Scalars['String']['input'];
  squad_ids: InputMaybe<Array<Scalars['Float']['input']>>;
  status: InputMaybe<Scalars['Boolean']['input']>;
  user_ids: InputMaybe<Array<Scalars['Float']['input']>>;
};

export type CreateTeacherCategoryInput = {
  icon: FileInput;
  status: Scalars['Boolean']['input'];
  title: Scalars['String']['input'];
};

export type CreateTeacherInput = {
  avatar: InputMaybe<FileInput>;
  description: Scalars['String']['input'];
  is_mentor: InputMaybe<Scalars['Boolean']['input']>;
  links: InputMaybe<Array<CreateTeacherLinkInput>>;
  mentorship_areas_ids: Array<Scalars['Float']['input']>;
  mobile_description: InputMaybe<Scalars['String']['input']>;
  name: Scalars['String']['input'];
  occupation_area: InputMaybe<Scalars['String']['input']>;
  price: InputMaybe<Scalars['Float']['input']>;
  roles: Array<Scalars['Float']['input']>;
  schedule: InputMaybe<Array<MentorScheduleValidator>>;
  status: Scalars['Boolean']['input'];
  tags: InputMaybe<Array<TeacherTagValidator>>;
  web_description: InputMaybe<Scalars['String']['input']>;
};

export type CreateTeacherLinkInput = {
  title: Scalars['String']['input'];
  url: Scalars['String']['input'];
};

export type CreditCardEntity = {
  brand: Scalars['String']['output'];
  created_at: Scalars['DateTime']['output'];
  expiration_month: Scalars['Float']['output'];
  expiration_year: Scalars['Float']['output'];
  external_credit_card_id: Scalars['String']['output'];
  first_six_digits: Scalars['String']['output'];
  id: Scalars['ID']['output'];
  is_active: Scalars['Boolean']['output'];
  last_four_digits: Scalars['String']['output'];
  payments: Maybe<Array<PaymentEntity>>;
  updated_at: Scalars['DateTime']['output'];
  user: Maybe<UserEntity>;
  user_id: Scalars['Float']['output'];
};

export type DeleteEnrollmentInput = {
  enrollment_id: Scalars['Float']['input'];
  has_to_cancel: Scalars['Boolean']['input'];
  user_id: InputMaybe<Scalars['Float']['input']>;
};

export type DeleteUserEnrollmentInput = {
  enrollment_id: Scalars['Float']['input'];
  has_to_cancel: Scalars['Boolean']['input'];
  user_id: Scalars['Float']['input'];
};

export enum EContractType {
  AIA = 'AIA',
  COURSE = 'COURSE',
  PLUS_B2B = 'PLUS_B2B',
  PRIVATE = 'PRIVATE'
}

export enum ECouponAvailability {
  ALL = 'ALL',
  COURSE = 'COURSE',
  SUBSCRIPTION = 'SUBSCRIPTION'
}

export enum ECourseChapterContentType {
  EXAM = 'EXAM',
  LECTURE = 'LECTURE',
  QUIZ = 'QUIZ'
}

export enum ECourseFinalizationCriteria {
  EXAM_AVERAGE = 'EXAM_AVERAGE',
  PROGRESS = 'PROGRESS'
}

export enum ECourseModality {
  INPERSON = 'inperson',
  LIVE = 'live',
  MASTERCLASS = 'masterclass',
  ONLINE = 'online'
}

export enum ECourseProgressStatus {
  COMPLETED = 'COMPLETED',
  IN_PROGRESS = 'IN_PROGRESS',
  NOT_STARTED = 'NOT_STARTED'
}

export enum EDayOfWeek {
  FRIDAY = 'FRIDAY',
  MONDAY = 'MONDAY',
  SATURDAY = 'SATURDAY',
  SUNDAY = 'SUNDAY',
  THURSDAY = 'THURSDAY',
  TUESDAY = 'TUESDAY',
  WEDNESDAY = 'WEDNESDAY'
}

export enum EEnrollmentStatus {
  ACTIVE = 'ACTIVE',
  CANCELED = 'CANCELED',
  EXPIRED = 'EXPIRED',
  INACTIVE = 'INACTIVE',
  RENEWED = 'RENEWED'
}

export enum EEnrollmentType {
  B2B = 'B2B',
  ECOMMERCE = 'ECOMMERCE',
  STUDENTSHIP = 'STUDENTSHIP',
  SUBSCRIPTION = 'SUBSCRIPTION',
  TRIAL = 'TRIAL'
}

export enum EExamGradeCriteria {
  HIGHEST = 'HIGHEST',
  LAST = 'LAST'
}

export enum EExamRandomizeQuestions {
  ALL = 'ALL',
  ANSWERS = 'ANSWERS',
  NONE = 'NONE',
  QUESTIONS = 'QUESTIONS'
}

export enum EListOrder {
  ASC = 'ASC',
  DESC = 'DESC'
}

export enum EListOrderBy {
  ACTIVITIES_COMPLETED = 'ACTIVITIES_COMPLETED',
  CATEGORY = 'CATEGORY',
  CNPJ = 'CNPJ',
  COMPANY_SQUAD = 'COMPANY_SQUAD',
  CONTEXT = 'CONTEXT',
  CREATED_AT = 'CREATED_AT',
  EMAIL = 'EMAIL',
  GRADE = 'GRADE',
  ID = 'ID',
  LAST_ACTIVITY_COMPLETED = 'LAST_ACTIVITY_COMPLETED',
  NAME = 'NAME',
  STATUS = 'STATUS',
  TEACHER = 'TEACHER',
  TITLE = 'TITLE',
  UPDATED_AT = 'UPDATED_AT',
  VIEW = 'VIEW'
}

export enum EMentorWorkPeriod {
  AFTERNOON = 'AFTERNOON',
  MORNING = 'MORNING',
  NIGHT = 'NIGHT'
}

export enum EPaymentAgents {
  AISE_LMS = 'AISE_LMS',
  PECEGE_PAY = 'PECEGE_PAY'
}

export enum EPaymentDiscountType {
  PERCENTAGE = 'PERCENTAGE',
  VALUE = 'VALUE'
}

export enum EPaymentMethod {
  APPLE_PAY = 'APPLE_PAY',
  BILLET = 'BILLET',
  CREDIT_CARD = 'CREDIT_CARD',
  GOOGLE_PAY = 'GOOGLE_PAY'
}

export enum EPaymentPlanModifierType {
  DISCOUNT = 'DISCOUNT',
  INCREMENT = 'INCREMENT'
}

export enum EPaymentStatus {
  PAYMENT_CANCELLED = 'PAYMENT_CANCELLED',
  PAYMENT_ERROR = 'PAYMENT_ERROR',
  PAYMENT_FAILURE = 'PAYMENT_FAILURE',
  PAYMENT_REFUNDED = 'PAYMENT_REFUNDED',
  PAYMENT_SUCCEED = 'PAYMENT_SUCCEED',
  REFUND_ERROR = 'REFUND_ERROR',
  WAITING_PAYMENT = 'WAITING_PAYMENT',
  WAITING_REFUND = 'WAITING_REFUND'
}

export enum EPaymentType {
  ONE_CHARGE = 'ONE_CHARGE',
  SUBSCRIPTION = 'SUBSCRIPTION'
}

export enum EPosition {
  AGENT = 'AGENT',
  AIDE = 'AIDE',
  ANALYST = 'ANALYST',
  APPRENTICE = 'APPRENTICE',
  ASSISTANT = 'ASSISTANT',
  ATTENDANT = 'ATTENDANT',
  CONSULTANT = 'CONSULTANT',
  COORDINATOR = 'COORDINATOR',
  DIRECTOR = 'DIRECTOR',
  INSTRUCTOR = 'INSTRUCTOR',
  INTERN = 'INTERN',
  MANAGER = 'MANAGER',
  MONITOR = 'MONITOR',
  OPERATOR = 'OPERATOR',
  SPECIALIST = 'SPECIALIST',
  SUPERVISOR = 'SUPERVISOR',
  TECHNICIAN = 'TECHNICIAN',
  TRAINEE = 'TRAINEE'
}

export enum ESeniority {
  JUNIOR = 'JUNIOR',
  MID_LEVEL = 'MID_LEVEL',
  SENIOR = 'SENIOR'
}

export enum ESolutionContext {
  ALL = 'ALL',
  ECOMMERCE = 'ECOMMERCE',
  PRIVATE = 'PRIVATE',
  PRO = 'PRO'
}

export enum EThirdPartyBillingPersonType {
  CNPJ = 'CNPJ',
  CPF = 'CPF',
  PASSPORT = 'PASSPORT'
}

export enum EUserCourseCertificationFileType {
  IMAGE = 'image',
  PDF = 'pdf'
}

export enum EUserGender {
  FEMALE = 'FEMALE',
  MALE = 'MALE',
  OTHER = 'OTHER',
  UNDEFINED = 'UNDEFINED'
}

export type EnrollUserInCourseInput = {
  course_id: Scalars['Float']['input'];
  enrollment_type: InputMaybe<EEnrollmentType>;
};

export type EnrollmentB2BMetadataEntity = {
  company: Maybe<CompanyEntity>;
  company_id: Scalars['ID']['output'];
  created_at: Scalars['DateTime']['output'];
  employees_count: Maybe<Scalars['Float']['output']>;
  enrollment: Maybe<EnrollmentEntity>;
  enrollment_id: Scalars['ID']['output'];
  licenses_count: Scalars['Float']['output'];
  name: Maybe<Scalars['String']['output']>;
  updated_at: Scalars['DateTime']['output'];
};


export type EnrollmentB2BMetadataEntityEmployeesCountArgs = {
  employee_status: InputMaybe<EEnrollmentStatus>;
};

export type EnrollmentEntity = {
  b2b_metadata: Maybe<EnrollmentB2BMetadataEntity>;
  courses: Maybe<Array<CourseEntity>>;
  created_at: Scalars['DateTime']['output'];
  expiration_date: Scalars['DateTime']['output'];
  id: Scalars['ID']['output'];
  metadata: Maybe<EnrollmentsMetadataEntity>;
  status: EEnrollmentStatus;
  type: EEnrollmentType;
  updated_at: Scalars['DateTime']['output'];
};

export type EnrollmentUserPivotEntity = {
  created_at: Scalars['DateTime']['output'];
  enrollment: Maybe<EnrollmentEntity>;
  enrollment_id: Scalars['Float']['output'];
  status: EEnrollmentStatus;
  user_id: Scalars['Float']['output'];
};

export type EnrollmentsMetadataEntity = {
  enrollment: Maybe<EnrollmentEntity>;
  enrollment_id: Scalars['Float']['output'];
  payment: Maybe<PaymentEntity>;
  paymentPlan: Maybe<PaymentPlanEntity>;
  payment_id: Scalars['Float']['output'];
  payment_plan_slug: Maybe<Scalars['String']['output']>;
};

export type ExamEntity = {
  course: Maybe<CourseEntity>;
  course_id: Scalars['Float']['output'];
  created_at: Scalars['DateTime']['output'];
  grade_criteria: EExamGradeCriteria;
  id: Scalars['Float']['output'];
  max_attempts: Maybe<Scalars['Float']['output']>;
  max_questions: Scalars['Float']['output'];
  name: Scalars['String']['output'];
  passing_grade: Scalars['Float']['output'];
  questions: Maybe<Array<ExamQuestionEntity>>;
  randomize_questions: EExamRandomizeQuestions;
  teacher: Maybe<TeacherEntity>;
  teacher_id: Scalars['Float']['output'];
  updated_at: Scalars['DateTime']['output'];
};

export type ExamQuestionAnswerEntity = {
  content: Scalars['String']['output'];
  created_at: Scalars['DateTime']['output'];
  exam_question_id: Scalars['Float']['output'];
  id: Scalars['Float']['output'];
  is_correct: Scalars['Boolean']['output'];
  question: Maybe<ExamQuestionEntity>;
  updated_at: Scalars['DateTime']['output'];
};

export type ExamQuestionEntity = {
  answers: Maybe<Array<ExamQuestionAnswerEntity>>;
  content: Scalars['String']['output'];
  created_at: Scalars['DateTime']['output'];
  exam: Maybe<ExamEntity>;
  exam_id: Scalars['Float']['output'];
  id: Scalars['Float']['output'];
  multiple_answers: Scalars['Boolean']['output'];
  updated_at: Scalars['DateTime']['output'];
};

export type FavoriteEntity = {
  course: Maybe<CourseEntity>;
  course_id: Scalars['Float']['output'];
  created_at: Scalars['DateTime']['output'];
  id: Scalars['Float']['output'];
  updated_at: Scalars['DateTime']['output'];
  user: Maybe<UserEntity>;
  user_id: Scalars['Float']['output'];
};

export type FileEntity = {
  created_at: Scalars['DateTime']['output'];
  fileName: Scalars['String']['output'];
  id: Scalars['ID']['output'];
  name: Scalars['String']['output'];
  subtype: Scalars['String']['output'];
  type: Scalars['String']['output'];
  updated_at: Scalars['DateTime']['output'];
  url: Scalars['String']['output'];
  url_thumbnail: Scalars['String']['output'];
};

export type FileInput = {
  fileName: Scalars['String']['input'];
  location: InputMaybe<Scalars['String']['input']>;
  name: Scalars['String']['input'];
  subtype: Scalars['String']['input'];
  type: Scalars['String']['input'];
  url: InputMaybe<Scalars['String']['input']>;
  url_thumbnail: InputMaybe<Scalars['String']['input']>;
};

export type GenerateB2bEnrollmentInput = {
  company_id: Scalars['Float']['input'];
  enrollment_expiration_date: Scalars['String']['input'];
  max_users: Scalars['Float']['input'];
  name: Scalars['String']['input'];
};

export type GetCourseProgressResponse = {
  completed_activities: Scalars['Int']['output'];
  completion_date: Maybe<Scalars['DateTime']['output']>;
  completion_percentage: Scalars['Float']['output'];
  last_lecture_id: Maybe<Scalars['Int']['output']>;
  last_lecture_stop_time: Maybe<Scalars['Int']['output']>;
  last_lecture_url: Maybe<Scalars['String']['output']>;
  total_activities: Scalars['Int']['output'];
};

export type MentorScheduleEntity = {
  day_of_week: EDayOfWeek;
  id: Scalars['ID']['output'];
  mentor: Maybe<TeacherEntity>;
  period: Array<Scalars['String']['output']>;
};

export type MentorScheduleValidator = {
  day_of_week: EDayOfWeek;
  period: Array<EMentorWorkPeriod>;
};

export type Mutation = {
  activateEnrollment: Scalars['Boolean']['output'];
  changeCourseStatus: CourseEntity;
  changeMentorCategoryStatus: TeacherCategoryEntity;
  completeCourseActivity: UserCourseProgressEntity;
  createCompany: CompanyEntity;
  createCompanyGroup: CompanyGroupEntity;
  createCompanySquad: CompanySquadEntity;
  createContract: ContractEntity;
  createCourse: CourseEntity;
  createCourseCategory: CourseCategoryEntity;
  createCourseChapter: CourseChapterEntity;
  createExam: ExamEntity;
  createFavorite: FavoriteEntity;
  createMentorCategory: TeacherCategoryEntity;
  createPaymentBenefit: PaymentBenefitsEntity;
  createPaymentCoupon: PaymentCouponEntity;
  createStudyPlan: StudyPlanEntity;
  createTeacher: TeacherEntity;
  createUserEnrollment: Array<EnrollmentEntity>;
  deleteCompany: Scalars['Boolean']['output'];
  deleteCompanyGroup: Scalars['Boolean']['output'];
  deleteCompanySquad: Scalars['Boolean']['output'];
  deleteContract: Scalars['Boolean']['output'];
  deleteCourse: Scalars['Boolean']['output'];
  deleteCourseCategory: Scalars['Boolean']['output'];
  deleteCourseChapter: Scalars['Boolean']['output'];
  deleteEnrollment: Scalars['Boolean']['output'];
  deleteExam: Scalars['Boolean']['output'];
  deleteFavorite: Scalars['Boolean']['output'];
  deleteMentorCategory: Scalars['Boolean']['output'];
  deletePaymentBenefit: Scalars['Boolean']['output'];
  deletePaymentCoupon: Scalars['Boolean']['output'];
  deleteStudyPlan: Scalars['Boolean']['output'];
  deleteTeacher: Scalars['Boolean']['output'];
  deleteUserEnrollment: Scalars['Boolean']['output'];
  enrollUserInCourse: Scalars['Boolean']['output'];
  generateB2bEnrollment: EnrollmentEntity;
  removeUserFromSquad: Scalars['Boolean']['output'];
  submitCourseFeedback: CourseFeedbackEntity;
  submitUserLectureNote: UserLectureNoteEntity;
  updateB2bEnrollment: EnrollmentEntity;
  updateB2bEnrollmentIdentification: EnrollmentEntity;
  updateCompany: CompanyEntity;
  updateCompanyGroup: CompanyGroupEntity;
  updateCompanySquad: CompanySquadEntity;
  updateContract: ContractEntity;
  updateCourse: CourseEntity;
  updateCourseCategory: CourseCategoryEntity;
  updateCourseChapter: CourseChapterEntity;
  updateCourseChaptersOrder: Array<CourseChapterEntity>;
  updateCourseContext: CourseEntity;
  updateEmployeeRole: UserEntity;
  updateExam: ExamEntity;
  updateMentorCategory: TeacherCategoryEntity;
  updatePaymentBenefit: PaymentBenefitsEntity;
  updatePaymentCoupon: PaymentCouponEntity;
  updateStudyPlan: StudyPlanEntity;
  updateTeacher: TeacherEntity;
};


export type MutationActivateEnrollmentArgs = {
  data: ActivateEnrollmentInput;
};


export type MutationChangeCourseStatusArgs = {
  id: Scalars['Int']['input'];
  status: Scalars['Boolean']['input'];
};


export type MutationChangeMentorCategoryStatusArgs = {
  id: Scalars['Int']['input'];
  status: Scalars['Boolean']['input'];
};


export type MutationCompleteCourseActivityArgs = {
  activity_id: Scalars['Int']['input'];
  course_id: Scalars['Int']['input'];
};


export type MutationCreateCompanyArgs = {
  data: CreateCompanyInput;
};


export type MutationCreateCompanyGroupArgs = {
  company_id: Scalars['Int']['input'];
  data: CreateCompanyGroupInput;
};


export type MutationCreateCompanySquadArgs = {
  data: CreateCompanySquadInput;
};


export type MutationCreateContractArgs = {
  data: CreateContractInput;
};


export type MutationCreateCourseArgs = {
  data: CreateCourseInput;
};


export type MutationCreateCourseCategoryArgs = {
  data: CreateCourseCategoryInput;
};


export type MutationCreateCourseChapterArgs = {
  data: CreateCourseChapterInput;
};


export type MutationCreateExamArgs = {
  data: CreateExamInput;
};


export type MutationCreateFavoriteArgs = {
  course_id: Scalars['Int']['input'];
};


export type MutationCreateMentorCategoryArgs = {
  data: CreateTeacherCategoryInput;
};


export type MutationCreatePaymentBenefitArgs = {
  data: CreatePaymentBenefitInput;
};


export type MutationCreatePaymentCouponArgs = {
  data: CreatePaymentCouponInput;
};


export type MutationCreateStudyPlanArgs = {
  data: CreateStudyPlanInput;
};


export type MutationCreateTeacherArgs = {
  data: CreateTeacherInput;
};


export type MutationCreateUserEnrollmentArgs = {
  data: CreateEnrollmentInput;
};


export type MutationDeleteCompanyArgs = {
  id: Scalars['Int']['input'];
};


export type MutationDeleteCompanyGroupArgs = {
  group_id: Scalars['Int']['input'];
};


export type MutationDeleteCompanySquadArgs = {
  id: Scalars['Int']['input'];
};


export type MutationDeleteContractArgs = {
  id: Scalars['Float']['input'];
};


export type MutationDeleteCourseArgs = {
  id: Scalars['Int']['input'];
};


export type MutationDeleteCourseCategoryArgs = {
  id: Scalars['Int']['input'];
};


export type MutationDeleteCourseChapterArgs = {
  id: Scalars['Int']['input'];
};


export type MutationDeleteEnrollmentArgs = {
  data: DeleteEnrollmentInput;
};


export type MutationDeleteExamArgs = {
  id: Scalars['Int']['input'];
};


export type MutationDeleteFavoriteArgs = {
  course_id: Scalars['Int']['input'];
};


export type MutationDeleteMentorCategoryArgs = {
  id: Scalars['Int']['input'];
};


export type MutationDeletePaymentBenefitArgs = {
  id: Scalars['Float']['input'];
};


export type MutationDeletePaymentCouponArgs = {
  code: Scalars['String']['input'];
};


export type MutationDeleteStudyPlanArgs = {
  id: Scalars['Float']['input'];
};


export type MutationDeleteTeacherArgs = {
  id: Scalars['Float']['input'];
};


export type MutationDeleteUserEnrollmentArgs = {
  data: DeleteUserEnrollmentInput;
};


export type MutationEnrollUserInCourseArgs = {
  data: EnrollUserInCourseInput;
};


export type MutationGenerateB2bEnrollmentArgs = {
  data: GenerateB2bEnrollmentInput;
};


export type MutationRemoveUserFromSquadArgs = {
  squad_id: Scalars['Float']['input'];
  user_id: Scalars['Float']['input'];
};


export type MutationSubmitCourseFeedbackArgs = {
  data: SubmitCourseFeedbackInput;
};


export type MutationSubmitUserLectureNoteArgs = {
  lecture_id: Scalars['Float']['input'];
  note: Scalars['String']['input'];
};


export type MutationUpdateB2bEnrollmentArgs = {
  data: UpdateB2bEnrollmentInput;
};


export type MutationUpdateB2bEnrollmentIdentificationArgs = {
  enrollment_id: Scalars['Int']['input'];
  name: Scalars['String']['input'];
};


export type MutationUpdateCompanyArgs = {
  data: UpdateCompanyInput;
};


export type MutationUpdateCompanyGroupArgs = {
  data: UpdateCompanyGroupInput;
};


export type MutationUpdateCompanySquadArgs = {
  data: UpdateCompanySquadInput;
};


export type MutationUpdateContractArgs = {
  data: UpdateContractInput;
};


export type MutationUpdateCourseArgs = {
  data: UpdateCourseInput;
  id: Scalars['Int']['input'];
};


export type MutationUpdateCourseCategoryArgs = {
  data: UpdateCourseCategoryInput;
};


export type MutationUpdateCourseChapterArgs = {
  data: UpdateCourseChapterInput;
};


export type MutationUpdateCourseChaptersOrderArgs = {
  data: UpdateCourseChapterOrderInput;
};


export type MutationUpdateCourseContextArgs = {
  context: ESolutionContext;
  id: Scalars['Int']['input'];
};


export type MutationUpdateEmployeeRoleArgs = {
  role_slugs: Array<Role>;
  user_id: Scalars['Int']['input'];
};


export type MutationUpdateExamArgs = {
  data: UpdateExamInput;
};


export type MutationUpdateMentorCategoryArgs = {
  data: UpdateTeacherCategoryInput;
};


export type MutationUpdatePaymentBenefitArgs = {
  data: UpdatePaymentBenefitInput;
};


export type MutationUpdatePaymentCouponArgs = {
  data: UpdatePaymentCouponInput;
};


export type MutationUpdateStudyPlanArgs = {
  data: UpdateStudyPlanInput;
};


export type MutationUpdateTeacherArgs = {
  data: UpdateTeacherInput;
};

export type PaginatedCategoriesResponse = {
  data: Array<TeacherCategoryEntity>;
  perPage: Scalars['Int']['output'];
  total: Scalars['Int']['output'];
};

export type PaginatedCompaniesResponse = {
  data: Array<CompanyEntity>;
  perPage: Scalars['Int']['output'];
  total: Scalars['Int']['output'];
};

export type PaginatedCompanyGroupUsersResponse = {
  data: Array<UserEntity>;
  perPage: Scalars['Int']['output'];
  total: Scalars['Int']['output'];
};

export type PaginatedCompanyGroupsResponse = {
  data: Array<CompanyGroupEntity>;
  perPage: Scalars['Int']['output'];
  total: Scalars['Int']['output'];
};

export type PaginatedCompanySquadUsersResponse = {
  data: Array<UserEntity>;
  perPage: Scalars['Int']['output'];
  total: Scalars['Int']['output'];
};

export type PaginatedCompanySquadsResponse = {
  data: Array<CompanySquadEntity>;
  perPage: Scalars['Int']['output'];
  total: Scalars['Int']['output'];
};

export type PaginatedContractsResponse = {
  data: Array<ContractEntity>;
  perPage: Scalars['Int']['output'];
  total: Scalars['Int']['output'];
};

export type PaginatedCourseActivitiesResponse = {
  data: Array<CourseChapterContentEntity>;
  perPage: Scalars['Int']['output'];
  total: Scalars['Int']['output'];
};

export type PaginatedCourseCategoryResponse = {
  data: Array<CourseCategoryEntity>;
  perPage: Scalars['Int']['output'];
  total: Scalars['Int']['output'];
};

export type PaginatedCourseFeedbackResponse = {
  data: Array<CourseFeedbackEntity>;
  perPage: Scalars['Int']['output'];
  total: Scalars['Int']['output'];
};

export type PaginatedCourseResponse = {
  data: Array<CourseEntity>;
  perPage: Scalars['Int']['output'];
  total: Scalars['Int']['output'];
};

export type PaginatedCourseSectionsResponse = {
  data: Array<SectionEntity>;
  perPage: Scalars['Int']['output'];
  total: Scalars['Int']['output'];
};

export type PaginatedEnrollmentsResponse = {
  data: Array<EnrollmentEntity>;
  perPage: Scalars['Int']['output'];
  total: Scalars['Int']['output'];
};

export type PaginatedExamResponse = {
  data: Array<ExamEntity>;
  perPage: Scalars['Int']['output'];
  total: Scalars['Int']['output'];
};

export type PaginatedFavoriteResponse = {
  data: Array<FavoriteEntity>;
  perPage: Scalars['Int']['output'];
  total: Scalars['Int']['output'];
};

export type PaginatedPaymentBenefitResponse = {
  data: Array<PaymentBenefitsEntity>;
  perPage: Scalars['Int']['output'];
  total: Scalars['Int']['output'];
};

export type PaginatedPaymentCouponResponse = {
  data: Array<PaymentCouponEntity>;
  perPage: Scalars['Int']['output'];
  total: Scalars['Int']['output'];
};

export type PaginatedPaymentReportResponse = {
  data: Array<ViewPaymentReport>;
  perPage: Scalars['Int']['output'];
  total: Scalars['Int']['output'];
};

export type PaginatedPaymentResponse = {
  data: Array<PaymentEntity>;
  perPage: Scalars['Int']['output'];
  total: Scalars['Int']['output'];
};

export type PaginatedStudyPlansResponse = {
  data: Array<StudyPlanEntity>;
  perPage: Scalars['Int']['output'];
  total: Scalars['Int']['output'];
};

export type PaginatedTeacherResponse = {
  data: Array<TeacherEntity>;
  perPage: Scalars['Int']['output'];
  total: Scalars['Int']['output'];
};

export type PaginatedUserContractsResponse = {
  data: Array<UserContractEntity>;
  perPage: Scalars['Int']['output'];
  total: Scalars['Int']['output'];
};

export type PaginatedUserExamSnapshotResponse = {
  data: Array<UserExamSnapshotEntity>;
  perPage: Scalars['Int']['output'];
  total: Scalars['Int']['output'];
};

export type PaginatedUsersResponse = {
  data: Array<UserEntity>;
  perPage: Scalars['Int']['output'];
  total: Scalars['Int']['output'];
};

export type PartnerEntity = {
  courses: Maybe<Array<CourseEntity>>;
  created_at: Scalars['DateTime']['output'];
  id: Scalars['ID']['output'];
  image: FileEntity;
  name: Scalars['String']['output'];
  redirect_url: Scalars['String']['output'];
  updated_at: Scalars['DateTime']['output'];
};

export type PaymentBenefitsEntity = {
  created_at: Scalars['DateTime']['output'];
  discount_code: Scalars['String']['output'];
  discount_percentage: Scalars['Float']['output'];
  id: Scalars['Float']['output'];
  payment_plan: Maybe<PaymentPlanEntity>;
  payment_plan_slug: Scalars['String']['output'];
  updated_at: Scalars['DateTime']['output'];
};

export type PaymentCouponEntity = {
  applicable_courses: Maybe<Array<CourseEntity>>;
  available_for: ECouponAvailability;
  code: Scalars['String']['output'];
  created_at: Scalars['DateTime']['output'];
  discount_type: EPaymentDiscountType;
  discount_value: Scalars['Float']['output'];
  end_date: Maybe<Scalars['DateTime']['output']>;
  is_cumulative: Scalars['Boolean']['output'];
  name: Scalars['String']['output'];
  quantity: Scalars['Float']['output'];
  remaining_coupons_count: Scalars['Float']['output'];
  start_date: Scalars['DateTime']['output'];
  status: Scalars['Boolean']['output'];
  updated_at: Scalars['DateTime']['output'];
  used_coupons_count: Scalars['Float']['output'];
};

export type PaymentEntity = {
  coupon: Maybe<Scalars['String']['output']>;
  course: Maybe<CourseEntity>;
  course_id: Maybe<Scalars['Float']['output']>;
  created_at: Scalars['DateTime']['output'];
  credit_cards: Maybe<Array<CreditCardEntity>>;
  discount_type: Maybe<EPaymentDiscountType>;
  discount_value: Maybe<Scalars['Float']['output']>;
  id: Scalars['Float']['output'];
  installment_value: Scalars['Float']['output'];
  parcels: Scalars['Float']['output'];
  payment_agent: EPaymentAgents;
  payment_method: EPaymentMethod;
  payment_plan: Maybe<Scalars['String']['output']>;
  payment_type: Maybe<EPaymentType>;
  status: EPaymentStatus;
  third_party_billing: Maybe<ThirdPartyBillingEntity>;
  total_payed_value: Scalars['Float']['output'];
  trace_id: Maybe<Scalars['String']['output']>;
  type: EPaymentType;
  updated_at: Scalars['DateTime']['output'];
  user: Maybe<UserEntity>;
  user_id: Maybe<Scalars['Float']['output']>;
};

export type PaymentPlanEntity = {
  created_at: Scalars['DateTime']['output'];
  discount_value: Scalars['Float']['output'];
  enabled: Maybe<Scalars['Boolean']['output']>;
  modifiers: Maybe<Array<PaymentPlanModifierEntity>>;
  name: Scalars['String']['output'];
  payment_value: Scalars['Float']['output'];
  price: Scalars['Float']['output'];
  representative_months: Scalars['Float']['output'];
  signup_days: Scalars['Float']['output'];
  skip_billing_months: Scalars['Float']['output'];
  slug: Scalars['String']['output'];
  updated_at: Scalars['DateTime']['output'];
  visible: Maybe<Scalars['Boolean']['output']>;
};

export type PaymentPlanModifierEntity = {
  created_at: Maybe<Scalars['DateTime']['output']>;
  installment_number: Scalars['Float']['output'];
  payment_plan: Maybe<PaymentPlanEntity>;
  payment_plan_slug: Scalars['String']['output'];
  type: EPaymentPlanModifierType;
  value: Scalars['Float']['output'];
};

export type Query = {
  GetCourseFeedbacks: Array<CourseFeedbackEntity>;
  b2bStatistics: B2bStatisticsModel;
  companies: PaginatedCompaniesResponse;
  company: CompanyEntity;
  companyGroup: CompanyGroupEntity;
  companyGroups: PaginatedCompanyGroupsResponse;
  companySquad: CompanySquadEntity;
  companySquads: PaginatedCompanySquadsResponse;
  contract: ContractEntity;
  contracts: PaginatedContractsResponse;
  course: CourseEntity;
  courseCategories: PaginatedCourseCategoryResponse;
  courseCategory: CourseCategoryEntity;
  courseSection: SectionEntity;
  courseSections: PaginatedCourseSectionsResponse;
  courses: PaginatedCourseResponse;
  enrolledCourses: PaginatedCourseResponse;
  enrollment: EnrollmentEntity;
  enrollments: PaginatedEnrollmentsResponse;
  exam: ExamEntity;
  exams: PaginatedExamResponse;
  favorites: PaginatedFavoriteResponse;
  getActivitiesCompleted: PaginatedCourseActivitiesResponse;
  getCourseProgress: GetCourseProgressResponse;
  getRelatedCourses: Array<CourseEntity>;
  getStudyPlanProgress: Scalars['Float']['output'];
  getSubscribedStudyPlans: Array<Maybe<StudyPlanEntity>>;
  keepWatchingCourses: PaginatedCourseResponse;
  listCourseFeedbacks: PaginatedCourseFeedbackResponse;
  mentorCategories: PaginatedCategoriesResponse;
  mentorCategory: TeacherCategoryEntity;
  paymentBenefit: PaymentBenefitsEntity;
  paymentBenefits: PaginatedPaymentBenefitResponse;
  paymentCoupon: Maybe<PaymentCouponEntity>;
  paymentCoupons: PaginatedPaymentCouponResponse;
  paymentPlan: PaymentPlanEntity;
  paymentPlans: Array<PaymentPlanEntity>;
  paymentReport: Maybe<PaginatedPaymentReportResponse>;
  payments: PaginatedPaymentResponse;
  profile: UserEntity;
  purchasedCourses: Array<CourseEntity>;
  studyPlan: StudyPlanEntity;
  studyPlans: PaginatedStudyPlansResponse;
  teacher: TeacherEntity;
  teachers: PaginatedTeacherResponse;
  unfinishedCourses: PaginatedCourseResponse;
  user: UserEntity;
  userCertifications: Array<UsersCourseCertificationPivotEntity>;
  userContract: UserContractEntity;
  userContracts: PaginatedUserContractsResponse;
  userCourses: PaginatedCourseResponse;
  userEnrollments: Array<EnrollmentEntity>;
  userExamSnapshots: PaginatedUserExamSnapshotResponse;
  userLectureNote: UserLectureNoteEntity;
  users: PaginatedUsersResponse;
};


export type QueryGetCourseFeedbacksArgs = {
  course_id: InputMaybe<Scalars['Int']['input']>;
};


export type QueryCompaniesArgs = {
  all: InputMaybe<Scalars['Boolean']['input']>;
  cnpj: InputMaybe<Scalars['String']['input']>;
  limit: InputMaybe<Scalars['Float']['input']>;
  name: InputMaybe<Scalars['String']['input']>;
  order: InputMaybe<EListOrder>;
  orderBy: InputMaybe<EListOrderBy>;
  page: InputMaybe<Scalars['Float']['input']>;
};


export type QueryCompanyArgs = {
  criteria: Scalars['String']['input'];
};


export type QueryCompanyGroupArgs = {
  id: Scalars['Int']['input'];
};


export type QueryCompanyGroupsArgs = {
  company_id: Scalars['Float']['input'];
  limit: InputMaybe<Scalars['Float']['input']>;
  name: InputMaybe<Scalars['String']['input']>;
  page: InputMaybe<Scalars['Float']['input']>;
};


export type QueryCompanySquadArgs = {
  id: Scalars['Int']['input'];
};


export type QueryCompanySquadsArgs = {
  company_id: InputMaybe<Scalars['Float']['input']>;
  limit: InputMaybe<Scalars['Float']['input']>;
  page: InputMaybe<Scalars['Float']['input']>;
  title: InputMaybe<Scalars['String']['input']>;
};


export type QueryContractArgs = {
  id: Scalars['Float']['input'];
};


export type QueryContractsArgs = {
  limit: InputMaybe<Scalars['Float']['input']>;
  page: InputMaybe<Scalars['Float']['input']>;
  title: InputMaybe<Scalars['String']['input']>;
  type: InputMaybe<EContractType>;
};


export type QueryCourseArgs = {
  all: InputMaybe<Scalars['Boolean']['input']>;
  criteria: Scalars['String']['input'];
};


export type QueryCourseCategoriesArgs = {
  all: Scalars['Boolean']['input'];
  context: InputMaybe<ESolutionContext>;
  exclude_id: InputMaybe<Scalars['Float']['input']>;
  limit: InputMaybe<Scalars['Float']['input']>;
  page: InputMaybe<Scalars['Float']['input']>;
  q: InputMaybe<Scalars['String']['input']>;
};


export type QueryCourseCategoryArgs = {
  criteria: Scalars['String']['input'];
};


export type QueryCourseSectionArgs = {
  id: Scalars['Int']['input'];
};


export type QueryCourseSectionsArgs = {
  all: InputMaybe<Scalars['Boolean']['input']>;
  context: InputMaybe<ESolutionContext>;
  order: EListOrder;
};


export type QueryCoursesArgs = {
  all: Scalars['Boolean']['input'];
  available_at: InputMaybe<Scalars['AvailabilityFilter']['input']>;
  category_id: InputMaybe<Scalars['Float']['input']>;
  category_ids: InputMaybe<Array<Scalars['Float']['input']>>;
  category_slug: InputMaybe<Scalars['String']['input']>;
  certification_id: InputMaybe<Scalars['Float']['input']>;
  course_progress_status: InputMaybe<ECourseProgressStatus>;
  course_title: InputMaybe<Scalars['String']['input']>;
  course_type: InputMaybe<Scalars['String']['input']>;
  featured: InputMaybe<Scalars['Boolean']['input']>;
  from_date: InputMaybe<Scalars['DateTime']['input']>;
  full: InputMaybe<Scalars['Boolean']['input']>;
  has_closed_captions: InputMaybe<Scalars['Boolean']['input']>;
  limit: InputMaybe<Scalars['Float']['input']>;
  limited: InputMaybe<Scalars['Boolean']['input']>;
  modality: InputMaybe<Array<ECourseModality>>;
  only_partners: InputMaybe<Scalars['Boolean']['input']>;
  order: InputMaybe<EListOrder>;
  orderBy: InputMaybe<EListOrderBy>;
  page: InputMaybe<Scalars['Float']['input']>;
  partner_id: InputMaybe<Scalars['Float']['input']>;
  price: InputMaybe<Scalars['String']['input']>;
  q: InputMaybe<Scalars['String']['input']>;
  tag_id: InputMaybe<Scalars['Float']['input']>;
  teacher_id: InputMaybe<Scalars['Float']['input']>;
  to_date: InputMaybe<Scalars['DateTime']['input']>;
  with_partners: InputMaybe<Scalars['Boolean']['input']>;
};


export type QueryEnrolledCoursesArgs = {
  course_status: InputMaybe<ECourseProgressStatus>;
  end_date: InputMaybe<Scalars['DateTime']['input']>;
  limit: InputMaybe<Scalars['Float']['input']>;
  page: InputMaybe<Scalars['Float']['input']>;
  start_date: InputMaybe<Scalars['DateTime']['input']>;
};


export type QueryEnrollmentArgs = {
  id: Scalars['Int']['input'];
};


export type QueryEnrollmentsArgs = {
  company_id: InputMaybe<Scalars['Float']['input']>;
  limit: InputMaybe<Scalars['Float']['input']>;
  page: Scalars['Float']['input'];
  status: InputMaybe<EEnrollmentStatus>;
  type: InputMaybe<EEnrollmentType>;
  types: InputMaybe<Array<EEnrollmentType>>;
  user_id: InputMaybe<Scalars['Float']['input']>;
};


export type QueryExamArgs = {
  id: Scalars['Int']['input'];
};


export type QueryExamsArgs = {
  course_id: InputMaybe<Scalars['Float']['input']>;
  limit: InputMaybe<Scalars['Float']['input']>;
  page: InputMaybe<Scalars['Float']['input']>;
  q: InputMaybe<Scalars['String']['input']>;
  teacher_id: InputMaybe<Scalars['Float']['input']>;
};


export type QueryFavoritesArgs = {
  category_id: InputMaybe<Scalars['Float']['input']>;
  course_progress_status: InputMaybe<ECourseProgressStatus>;
  course_title: InputMaybe<Scalars['String']['input']>;
  limit: InputMaybe<Scalars['Float']['input']>;
  page: Scalars['Float']['input'];
  teacher_id: InputMaybe<Scalars['Float']['input']>;
};


export type QueryGetActivitiesCompletedArgs = {
  activity_type: InputMaybe<ECourseChapterContentType>;
  course_id: InputMaybe<Scalars['Int']['input']>;
  limit: InputMaybe<Scalars['Int']['input']>;
  page: InputMaybe<Scalars['Int']['input']>;
};


export type QueryGetCourseProgressArgs = {
  course_id: Scalars['Int']['input'];
};


export type QueryGetRelatedCoursesArgs = {
  context: InputMaybe<ESolutionContext>;
  criteria: Scalars['String']['input'];
};


export type QueryGetStudyPlanProgressArgs = {
  study_plan_id: Scalars['Float']['input'];
};


export type QueryGetSubscribedStudyPlansArgs = {
  limit: InputMaybe<Scalars['Float']['input']>;
  onlyPDI: InputMaybe<Scalars['Boolean']['input']>;
  page: InputMaybe<Scalars['Float']['input']>;
};


export type QueryKeepWatchingCoursesArgs = {
  limit: InputMaybe<Scalars['Float']['input']>;
  page: InputMaybe<Scalars['Float']['input']>;
};


export type QueryListCourseFeedbacksArgs = {
  course_id: InputMaybe<Scalars['Float']['input']>;
  limit: InputMaybe<Scalars['Float']['input']>;
  page: InputMaybe<Scalars['Float']['input']>;
  user_id: InputMaybe<Scalars['Float']['input']>;
};


export type QueryMentorCategoriesArgs = {
  all: Scalars['Boolean']['input'];
  limit: InputMaybe<Scalars['Float']['input']>;
  page: Scalars['Float']['input'];
  q: InputMaybe<Scalars['String']['input']>;
};


export type QueryMentorCategoryArgs = {
  id: Scalars['Int']['input'];
};


export type QueryPaymentBenefitArgs = {
  id: Scalars['Float']['input'];
};


export type QueryPaymentBenefitsArgs = {
  code: InputMaybe<Scalars['String']['input']>;
  limit: InputMaybe<Scalars['Float']['input']>;
  page: InputMaybe<Scalars['Float']['input']>;
  payment_plan_slug: InputMaybe<Scalars['String']['input']>;
};


export type QueryPaymentCouponArgs = {
  code: Scalars['String']['input'];
};


export type QueryPaymentCouponsArgs = {
  code: InputMaybe<Scalars['String']['input']>;
  limit: InputMaybe<Scalars['Float']['input']>;
  order: InputMaybe<EListOrder>;
  orderBy: InputMaybe<Scalars['String']['input']>;
  page: InputMaybe<Scalars['Float']['input']>;
};


export type QueryPaymentPlanArgs = {
  slug: Scalars['String']['input'];
};


export type QueryPaymentReportArgs = {
  end_date: Scalars['DateTime']['input'];
  start_date: Scalars['DateTime']['input'];
};


export type QueryPaymentsArgs = {
  end_date: InputMaybe<Scalars['DateTime']['input']>;
  limit: InputMaybe<Scalars['Float']['input']>;
  only_plus_b2c: InputMaybe<Scalars['Boolean']['input']>;
  page: InputMaybe<Scalars['Float']['input']>;
  start_date: InputMaybe<Scalars['DateTime']['input']>;
};


export type QueryPurchasedCoursesArgs = {
  all: InputMaybe<Scalars['Boolean']['input']>;
  category_id: InputMaybe<Scalars['Float']['input']>;
  context: InputMaybe<ESolutionContext>;
  course_progress_status: InputMaybe<ECourseProgressStatus>;
  course_title: InputMaybe<Scalars['String']['input']>;
  teacher_id: InputMaybe<Scalars['Float']['input']>;
  user_id: InputMaybe<Scalars['Float']['input']>;
  with_expired: InputMaybe<Scalars['Boolean']['input']>;
};


export type QueryStudyPlanArgs = {
  id: Scalars['Float']['input'];
};


export type QueryStudyPlansArgs = {
  company_id: Scalars['Float']['input'];
  end_date: InputMaybe<Scalars['DateTime']['input']>;
  limit: InputMaybe<Scalars['Float']['input']>;
  name: InputMaybe<Scalars['String']['input']>;
  onlyPDI: InputMaybe<Scalars['Boolean']['input']>;
  page: InputMaybe<Scalars['Float']['input']>;
  status: InputMaybe<Scalars['Boolean']['input']>;
  user_id: InputMaybe<Scalars['Float']['input']>;
};


export type QueryTeacherArgs = {
  criteria: Scalars['String']['input'];
};


export type QueryTeachersArgs = {
  all: InputMaybe<Scalars['String']['input']>;
  limit: InputMaybe<Scalars['String']['input']>;
  onlyMentors: InputMaybe<Scalars['String']['input']>;
  order: InputMaybe<EListOrder>;
  page: InputMaybe<Scalars['String']['input']>;
  q: InputMaybe<Scalars['String']['input']>;
};


export type QueryUnfinishedCoursesArgs = {
  course_status: InputMaybe<ECourseProgressStatus>;
  end_date: InputMaybe<Scalars['DateTime']['input']>;
  limit: InputMaybe<Scalars['Float']['input']>;
  page: InputMaybe<Scalars['Float']['input']>;
  start_date: InputMaybe<Scalars['DateTime']['input']>;
};


export type QueryUserArgs = {
  id: Scalars['Int']['input'];
};


export type QueryUserCertificationsArgs = {
  category_id: InputMaybe<Scalars['Float']['input']>;
  course_title: InputMaybe<Scalars['String']['input']>;
  file_type: EUserCourseCertificationFileType;
  teacher_id: InputMaybe<Scalars['Float']['input']>;
  user_id: InputMaybe<Scalars['Float']['input']>;
};


export type QueryUserContractArgs = {
  signed: Scalars['Boolean']['input'];
  user_contract_id: Scalars['Float']['input'];
};


export type QueryUserContractsArgs = {
  limit?: InputMaybe<Scalars['Int']['input']>;
  page?: InputMaybe<Scalars['Int']['input']>;
  signed: InputMaybe<Scalars['Boolean']['input']>;
};


export type QueryUserCoursesArgs = {
  all: Scalars['Boolean']['input'];
  available_at: InputMaybe<Scalars['AvailabilityFilter']['input']>;
  category_id: InputMaybe<Scalars['Float']['input']>;
  category_ids: InputMaybe<Array<Scalars['Float']['input']>>;
  category_slug: InputMaybe<Scalars['String']['input']>;
  certification_id: InputMaybe<Scalars['Float']['input']>;
  course_progress_status: InputMaybe<ECourseProgressStatus>;
  course_title: InputMaybe<Scalars['String']['input']>;
  course_type: InputMaybe<Scalars['String']['input']>;
  featured: InputMaybe<Scalars['Boolean']['input']>;
  from_date: InputMaybe<Scalars['DateTime']['input']>;
  full: InputMaybe<Scalars['Boolean']['input']>;
  has_closed_captions: InputMaybe<Scalars['Boolean']['input']>;
  limit: InputMaybe<Scalars['Float']['input']>;
  limited: InputMaybe<Scalars['Boolean']['input']>;
  modality: InputMaybe<Array<ECourseModality>>;
  only_partners: InputMaybe<Scalars['Boolean']['input']>;
  order: InputMaybe<EListOrder>;
  orderBy: InputMaybe<EListOrderBy>;
  page: InputMaybe<Scalars['Float']['input']>;
  partner_id: InputMaybe<Scalars['Float']['input']>;
  price: InputMaybe<Scalars['String']['input']>;
  q: InputMaybe<Scalars['String']['input']>;
  tag_id: InputMaybe<Scalars['Float']['input']>;
  teacher_id: InputMaybe<Scalars['Float']['input']>;
  to_date: InputMaybe<Scalars['DateTime']['input']>;
  with_partners: InputMaybe<Scalars['Boolean']['input']>;
};


export type QueryUserEnrollmentsArgs = {
  company_id: InputMaybe<Scalars['Float']['input']>;
  status: InputMaybe<EEnrollmentStatus>;
};


export type QueryUserExamSnapshotsArgs = {
  exam_id: InputMaybe<Scalars['Float']['input']>;
  is_finished: InputMaybe<Scalars['Boolean']['input']>;
  limit: InputMaybe<Scalars['Float']['input']>;
  order: InputMaybe<EListOrder>;
  orderBy: InputMaybe<EListOrderBy>;
  page: InputMaybe<Scalars['Float']['input']>;
};


export type QueryUserLectureNoteArgs = {
  lecture_id: Scalars['Int']['input'];
};


export type QueryUsersArgs = {
  all: InputMaybe<Scalars['Boolean']['input']>;
  company_id: InputMaybe<Scalars['Float']['input']>;
  enrollment_id: InputMaybe<Scalars['Float']['input']>;
  from_date: InputMaybe<Scalars['String']['input']>;
  hasSquad: InputMaybe<Scalars['Boolean']['input']>;
  limit: Scalars['Float']['input'];
  order: EListOrder;
  orderBy: EListOrderBy;
  page: InputMaybe<Scalars['Float']['input']>;
  q: InputMaybe<Scalars['String']['input']>;
  role: InputMaybe<Scalars['String']['input']>;
  roles: InputMaybe<Array<Scalars['String']['input']>>;
  squad_id: InputMaybe<Scalars['Float']['input']>;
  to_date: InputMaybe<Scalars['String']['input']>;
};

export type RelatedCoursesInput = {
  id: Scalars['Int']['input'];
};

export enum Role {
  ADMIN = 'ADMIN',
  AUTHOR = 'AUTHOR',
  B2B_MANAGER = 'B2B_MANAGER',
  B2B_USER = 'B2B_USER',
  USER = 'USER'
}

export type RoleEntity = {
  created_at: Scalars['DateTime']['output'];
  description: Scalars['String']['output'];
  id: Scalars['Float']['output'];
  name: Scalars['String']['output'];
  slug: Scalars['String']['output'];
  updated_at: Scalars['DateTime']['output'];
};

export type SectionEntity = {
  active: Scalars['Boolean']['output'];
  available_at: ESolutionContext;
  course_section: Maybe<CourseSectionEntity>;
  created_at: Scalars['DateTime']['output'];
  cta_title: Maybe<Scalars['String']['output']>;
  cta_url: Maybe<Scalars['String']['output']>;
  end_date: Maybe<Scalars['DateTime']['output']>;
  id: Scalars['Float']['output'];
  is_loop: Maybe<Scalars['Boolean']['output']>;
  location: Scalars['String']['output'];
  start_date: Maybe<Scalars['DateTime']['output']>;
  title: Scalars['String']['output'];
  updated_at: Scalars['DateTime']['output'];
};

export type StateEntity = {
  country: CountryEntity;
  country_id: Scalars['Float']['output'];
  created_at: Scalars['DateTime']['output'];
  id: Scalars['Float']['output'];
  initials: Scalars['String']['output'];
  name: Scalars['String']['output'];
  updated_at: Scalars['DateTime']['output'];
};

export type StatisticsData = {
  count: Scalars['Float']['output'];
  growth: Scalars['Float']['output'];
};

export type StudyPlanCoursePivot = {
  course_id: Scalars['Float']['input'];
  order: Scalars['Float']['input'];
};

export type StudyPlanCoursePivotEntity = {
  course: Maybe<CourseEntity>;
  course_id: Scalars['Float']['output'];
  created_at: Scalars['DateTime']['output'];
  order: Scalars['Float']['output'];
  plan_id: Scalars['Float']['output'];
};

export type StudyPlanEntity = {
  ai_generated: Maybe<Scalars['Boolean']['output']>;
  company_id: Scalars['Float']['output'];
  coursesCount: Scalars['Float']['output'];
  courses_pivot: Maybe<Array<StudyPlanCoursePivotEntity>>;
  created_at: Scalars['DateTime']['output'];
  description: Scalars['String']['output'];
  end_date: Maybe<Scalars['DateTime']['output']>;
  id: Scalars['Float']['output'];
  is_pdi: Scalars['Boolean']['output'];
  name: Scalars['String']['output'];
  squads: Maybe<Array<CompanySquadEntity>>;
  squadsCount: Scalars['Float']['output'];
  status: Maybe<Scalars['Boolean']['output']>;
  updated_at: Scalars['DateTime']['output'];
  user_progress: Maybe<Scalars['Float']['output']>;
  users: Maybe<Array<UserEntity>>;
  usersCount: Scalars['Float']['output'];
  users_completed_count: Maybe<Scalars['Float']['output']>;
};


export type StudyPlanEntityCoursesPivotArgs = {
  category_id: InputMaybe<Scalars['Float']['input']>;
  course_progress_status: InputMaybe<ECourseProgressStatus>;
  course_title: InputMaybe<Scalars['String']['input']>;
  teacher_id: InputMaybe<Scalars['Float']['input']>;
};

export type SubmitCourseFeedbackInput = {
  course_id: Scalars['Float']['input'];
  feedback: InputMaybe<Scalars['String']['input']>;
  share_rating: Scalars['Float']['input'];
  teacher_rating: Scalars['Float']['input'];
};

export type TeacherCategoryEntity = {
  created_at: Scalars['DateTime']['output'];
  icon: Maybe<FileEntity>;
  icon_id: Maybe<Scalars['Int']['output']>;
  id: Scalars['Int']['output'];
  slug: Scalars['String']['output'];
  status: Scalars['Boolean']['output'];
  teachers: Maybe<Array<TeacherEntity>>;
  title: Scalars['String']['output'];
  updated_at: Scalars['DateTime']['output'];
};

export type TeacherEntity = {
  avatar: Maybe<FileEntity>;
  courses: Maybe<Array<CourseEntity>>;
  created_at: Scalars['DateTime']['output'];
  description: Maybe<Scalars['String']['output']>;
  id: Scalars['ID']['output'];
  is_mentor: Maybe<Scalars['Boolean']['output']>;
  is_plus_b2c: Scalars['Boolean']['output'];
  links: Maybe<Array<TeacherLinksEntity>>;
  mentorship_areas: Maybe<Array<TeacherCategoryEntity>>;
  mobile_description: Maybe<Scalars['String']['output']>;
  name: Scalars['String']['output'];
  occupation_area: Maybe<Scalars['String']['output']>;
  photo_id: Maybe<Scalars['Int']['output']>;
  price: Maybe<Scalars['Float']['output']>;
  roles: Maybe<Array<TeacherRoleEntity>>;
  schedule: Maybe<Array<MentorScheduleEntity>>;
  slug: Scalars['String']['output'];
  status: Scalars['Boolean']['output'];
  tags: Maybe<Array<TeacherTagEntity>>;
  type: Scalars['String']['output'];
  updated_at: Scalars['DateTime']['output'];
  web_description: Maybe<Scalars['String']['output']>;
};

export type TeacherLinksEntity = {
  created_at: Scalars['DateTime']['output'];
  id: Scalars['ID']['output'];
  teacher: TeacherEntity;
  title: Scalars['String']['output'];
  updated_at: Scalars['DateTime']['output'];
  url: Scalars['String']['output'];
};

export type TeacherRoleEntity = {
  created_at: Scalars['DateTime']['output'];
  id: Scalars['ID']['output'];
  teachers: Maybe<Array<TeacherEntity>>;
  title: Scalars['String']['output'];
  updated_at: Scalars['DateTime']['output'];
};

export type TeacherTagEntity = {
  created_at: Scalars['DateTime']['output'];
  id: Scalars['ID']['output'];
  name: Scalars['String']['output'];
  teachers: Maybe<Array<TeacherEntity>>;
};

export type TeacherTagValidator = {
  id: Scalars['Float']['input'];
};

export type ThirdPartyBillingAddressEntity = {
  city_id: Maybe<Scalars['Float']['output']>;
  city_name: Maybe<Scalars['String']['output']>;
  complement: Scalars['String']['output'];
  country_id: Scalars['Float']['output'];
  neighboorhood: Scalars['String']['output'];
  place_number: Scalars['Float']['output'];
  state_id: Maybe<Scalars['Float']['output']>;
  state_name: Maybe<Scalars['String']['output']>;
  street: Scalars['String']['output'];
  third_party_billing_id: Scalars['Float']['output'];
  zip_code: Scalars['String']['output'];
};

export type ThirdPartyBillingEntity = {
  address: ThirdPartyBillingAddressEntity;
  document: Scalars['String']['output'];
  email: Scalars['String']['output'];
  payment_id: Scalars['Float']['output'];
  person_name: Scalars['String']['output'];
  person_type: EThirdPartyBillingPersonType;
  phone_number: Scalars['String']['output'];
};

export type UpdateB2bEnrollmentInput = {
  enrollment_id: Scalars['Float']['input'];
  expiration_date: InputMaybe<Scalars['DateTime']['input']>;
  licenses_count: InputMaybe<Scalars['Float']['input']>;
  name: InputMaybe<Scalars['String']['input']>;
};

export type UpdateCompanyGroupInput = {
  id: Scalars['Float']['input'];
  name: InputMaybe<Scalars['String']['input']>;
  user_ids_to_add: InputMaybe<Array<Scalars['Float']['input']>>;
  user_ids_to_remove: InputMaybe<Array<Scalars['Float']['input']>>;
};

export type UpdateCompanyInput = {
  cnpj: InputMaybe<Scalars['String']['input']>;
  email: InputMaybe<Scalars['String']['input']>;
  id: Scalars['Float']['input'];
  name: InputMaybe<Scalars['String']['input']>;
  phone_number: InputMaybe<Scalars['String']['input']>;
  state_registration: InputMaybe<Scalars['String']['input']>;
  status: InputMaybe<Scalars['Boolean']['input']>;
};

export type UpdateCompanySquadInput = {
  company_id: InputMaybe<Scalars['Float']['input']>;
  id: Scalars['Float']['input'];
  title: InputMaybe<Scalars['String']['input']>;
  user_ids: InputMaybe<Array<Scalars['Float']['input']>>;
};

export type UpdateContractInput = {
  content: InputMaybe<Scalars['String']['input']>;
  contract_type: InputMaybe<EContractType>;
  id: Scalars['Float']['input'];
  title: InputMaybe<Scalars['String']['input']>;
};

export type UpdateCourseCategoryInput = {
  cover: InputMaybe<FileInput>;
  description: InputMaybe<Scalars['String']['input']>;
  icon: InputMaybe<FileInput>;
  id: Scalars['Float']['input'];
  status: InputMaybe<Scalars['Boolean']['input']>;
  title: InputMaybe<Scalars['String']['input']>;
};

export type UpdateCourseChapterContentInput = {
  content: Scalars['String']['input'];
  description: InputMaybe<Scalars['String']['input']>;
  id: InputMaybe<Scalars['Float']['input']>;
  order: Scalars['Float']['input'];
  title: Scalars['String']['input'];
  type: ECourseChapterContentType;
};

export type UpdateCourseChapterInput = {
  assets: InputMaybe<Array<FileInput>>;
  contents: InputMaybe<Array<UpdateCourseChapterContentInput>>;
  course_id: InputMaybe<Scalars['Float']['input']>;
  duration: Scalars['Float']['input'];
  id: Scalars['Float']['input'];
  order: InputMaybe<Scalars['Float']['input']>;
  title: InputMaybe<Scalars['String']['input']>;
};

export type UpdateCourseChapterOrderInput = {
  chapters: Array<ChaptersPivot>;
};

export type UpdateCourseInput = {
  assets: InputMaybe<Array<FileInput>>;
  available_at: InputMaybe<ESolutionContext>;
  categories: InputMaybe<Array<Scalars['Int']['input']>>;
  certification_id: InputMaybe<Scalars['Int']['input']>;
  cost_center_id: InputMaybe<Scalars['String']['input']>;
  course_contents: InputMaybe<Array<CourseContentsUpdateInput>>;
  cover: InputMaybe<FileInput>;
  cover_hover: InputMaybe<FileInput>;
  cta: InputMaybe<CreateCtaInput>;
  description: InputMaybe<Scalars['String']['input']>;
  discount_percent: InputMaybe<Scalars['Int']['input']>;
  features: InputMaybe<Array<CourseFeatureInput>>;
  finalization_criteria: InputMaybe<Array<CourseFinalizationCriteriaValidator>>;
  has_closed_captions: InputMaybe<Scalars['Boolean']['input']>;
  is_partner_course: InputMaybe<Scalars['Boolean']['input']>;
  lms_course_id: InputMaybe<Scalars['Int']['input']>;
  modality: InputMaybe<ECourseModality>;
  partner_id: InputMaybe<Scalars['Int']['input']>;
  payment_installments: InputMaybe<Scalars['Int']['input']>;
  price: InputMaybe<Scalars['Float']['input']>;
  promo_code: InputMaybe<Scalars['String']['input']>;
  related_courses: InputMaybe<Array<RelatedCoursesInput>>;
  release_date: InputMaybe<Scalars['String']['input']>;
  start: InputMaybe<Scalars['String']['input']>;
  status: InputMaybe<Scalars['Boolean']['input']>;
  subtitle: InputMaybe<Scalars['String']['input']>;
  teachers: InputMaybe<Array<Scalars['Int']['input']>>;
  title: InputMaybe<Scalars['String']['input']>;
  trailer_url: InputMaybe<Scalars['String']['input']>;
};

export type UpdateExamInput = {
  course_id: InputMaybe<Scalars['Float']['input']>;
  grade_criteria: InputMaybe<EExamGradeCriteria>;
  id: Scalars['Float']['input'];
  max_attempts: InputMaybe<Scalars['Float']['input']>;
  max_questions: InputMaybe<Scalars['Float']['input']>;
  name: InputMaybe<Scalars['String']['input']>;
  passing_grade: InputMaybe<Scalars['Float']['input']>;
  questions: InputMaybe<Array<UpdateExamQuestionInput>>;
  randomize_questions: InputMaybe<EExamRandomizeQuestions>;
  teacher_id: InputMaybe<Scalars['Float']['input']>;
};

export type UpdateExamQuestionAnswerInput = {
  content: InputMaybe<Scalars['String']['input']>;
  is_correct: InputMaybe<Scalars['Boolean']['input']>;
};

export type UpdateExamQuestionInput = {
  answers: InputMaybe<Array<UpdateExamQuestionAnswerInput>>;
  content: InputMaybe<Scalars['String']['input']>;
  exam_id: InputMaybe<Scalars['Float']['input']>;
  id: InputMaybe<Scalars['Float']['input']>;
};

export type UpdatePaymentBenefitInput = {
  discount_code: InputMaybe<Scalars['String']['input']>;
  discount_percentage: InputMaybe<Scalars['Float']['input']>;
  id: Scalars['Float']['input'];
  payment_plan_slug: InputMaybe<Scalars['String']['input']>;
};

export type UpdatePaymentCouponInput = {
  code: Scalars['String']['input'];
  end_date: InputMaybe<Scalars['DateTime']['input']>;
  name: InputMaybe<Scalars['String']['input']>;
  quantity: InputMaybe<Scalars['Float']['input']>;
  start_date: InputMaybe<Scalars['DateTime']['input']>;
  status: InputMaybe<Scalars['Boolean']['input']>;
};

export type UpdateStudyPlanInput = {
  ai_generated: InputMaybe<Scalars['Boolean']['input']>;
  courses: InputMaybe<Array<StudyPlanCoursePivot>>;
  description: InputMaybe<Scalars['String']['input']>;
  end_date: InputMaybe<Scalars['DateTime']['input']>;
  id: Scalars['Float']['input'];
  is_for_all_courses: InputMaybe<Scalars['Boolean']['input']>;
  is_for_all_squads: InputMaybe<Scalars['Boolean']['input']>;
  is_for_all_users: InputMaybe<Scalars['Boolean']['input']>;
  is_pdi: InputMaybe<Scalars['Boolean']['input']>;
  name: InputMaybe<Scalars['String']['input']>;
  notify: InputMaybe<Scalars['Boolean']['input']>;
  squad_ids: InputMaybe<Array<Scalars['Float']['input']>>;
  status: InputMaybe<Scalars['Boolean']['input']>;
  user_ids: InputMaybe<Array<Scalars['Float']['input']>>;
};

export type UpdateTeacherCategoryInput = {
  icon: InputMaybe<FileInput>;
  id: Scalars['Int']['input'];
  status: InputMaybe<Scalars['Boolean']['input']>;
  title: InputMaybe<Scalars['String']['input']>;
};

export type UpdateTeacherInput = {
  avatar: InputMaybe<FileInput>;
  description: InputMaybe<Scalars['String']['input']>;
  id: Scalars['Float']['input'];
  is_mentor: InputMaybe<Scalars['Boolean']['input']>;
  links: InputMaybe<Array<CreateTeacherLinkInput>>;
  mentorship_areas_ids: InputMaybe<Array<Scalars['Float']['input']>>;
  mobile_description: InputMaybe<Scalars['String']['input']>;
  name: InputMaybe<Scalars['String']['input']>;
  occupation_area: InputMaybe<Scalars['String']['input']>;
  price: InputMaybe<Scalars['Float']['input']>;
  roles: InputMaybe<Array<Scalars['Float']['input']>>;
  schedule: InputMaybe<Array<MentorScheduleValidator>>;
  status: InputMaybe<Scalars['Boolean']['input']>;
  tags: InputMaybe<Array<TeacherTagValidator>>;
  web_description: InputMaybe<Scalars['String']['input']>;
};

export type UserAddressEntity = {
  city: Maybe<CityEntity>;
  city_id: Scalars['Float']['output'];
  city_name: Scalars['String']['output'];
  complement: Scalars['String']['output'];
  country_id: Scalars['Float']['output'];
  created_at: Scalars['DateTime']['output'];
  id: Scalars['Float']['output'];
  neighboorhood: Scalars['String']['output'];
  place_number: Scalars['String']['output'];
  state: Maybe<StateEntity>;
  state_id: Scalars['Float']['output'];
  state_name: Scalars['String']['output'];
  street: Scalars['String']['output'];
  updated_at: Scalars['DateTime']['output'];
  zip_code: Scalars['String']['output'];
};

export type UserContractEntity = {
  content: Scalars['String']['output'];
  contract: Maybe<ContractEntity>;
  contract_id: Scalars['Float']['output'];
  course: Maybe<CourseEntity>;
  course_id: Maybe<Scalars['Float']['output']>;
  created_at: Scalars['DateTime']['output'];
  id: Scalars['Float']['output'];
  parameters: Scalars['String']['output'];
  signed: Scalars['Boolean']['output'];
  signed_at: Maybe<Scalars['DateTime']['output']>;
  user_id: Scalars['Float']['output'];
};

export type UserCourseProgressEntity = {
  activities_completed: Scalars['Int']['output'];
  completion_date: Scalars['DateTime']['output'];
  course: Maybe<CourseEntity>;
  course_id: Scalars['Float']['output'];
  status: ECourseProgressStatus;
  updated_at: Scalars['DateTime']['output'];
  user_id: Scalars['Float']['output'];
};

export type UserEngagementRankingDto = {
  activities_completed: Scalars['Int']['output'];
  courses_completed: Scalars['Int']['output'];
  user: UserEntity;
};


export type UserEngagementRankingDtoActivitiesCompletedArgs = {
  company_id: InputMaybe<Scalars['Int']['input']>;
  end_date: InputMaybe<Scalars['DateTime']['input']>;
  limit: InputMaybe<Scalars['Int']['input']>;
  start_date: InputMaybe<Scalars['DateTime']['input']>;
};


export type UserEngagementRankingDtoCoursesCompletedArgs = {
  company_id: InputMaybe<Scalars['Int']['input']>;
  end_date: InputMaybe<Scalars['DateTime']['input']>;
  limit: InputMaybe<Scalars['Int']['input']>;
  start_date: InputMaybe<Scalars['DateTime']['input']>;
};

export type UserEntity = {
  address: Maybe<UserAddressEntity>;
  address_id: Maybe<Scalars['Float']['output']>;
  admitted_at: Maybe<Scalars['DateTime']['output']>;
  avatar: Maybe<FileEntity>;
  avatar_id: Maybe<Scalars['Float']['output']>;
  birthdate: Maybe<Scalars['DateTime']['output']>;
  blocked_at: Maybe<Scalars['DateTime']['output']>;
  company_group_users_pivot: Maybe<Array<CompanyGroupUsersPivotEntity>>;
  cpf: Maybe<Scalars['String']['output']>;
  created_at: Scalars['DateTime']['output'];
  email: Scalars['String']['output'];
  email_score: Maybe<Scalars['Float']['output']>;
  enrolled_courses: Maybe<PaginatedCourseResponse>;
  enrollments: Maybe<Array<EnrollmentEntity>>;
  enrollments_pivot: Maybe<Array<EnrollmentUserPivotEntity>>;
  foreign_document: Maybe<Scalars['String']['output']>;
  gender: Maybe<EUserGender>;
  hidden_at: Maybe<Scalars['DateTime']['output']>;
  id: Scalars['Float']['output'];
  is_blocked: Scalars['Boolean']['output'];
  is_hidden: Scalars['Boolean']['output'];
  last_login: Maybe<Scalars['DateTime']['output']>;
  lms_user_id: Maybe<Scalars['Float']['output']>;
  metadata: Maybe<UserMetadataEntity>;
  name: Scalars['String']['output'];
  nationality_id: Maybe<Scalars['Float']['output']>;
  occupation: Maybe<Scalars['String']['output']>;
  payment_plan: Maybe<PaymentPlanEntity>;
  phone_number: Maybe<Scalars['String']['output']>;
  position: Maybe<EPosition>;
  roles: Maybe<Array<RoleEntity>>;
  seniority: Maybe<ESeniority>;
  status: Scalars['Boolean']['output'];
  updated_at: Scalars['DateTime']['output'];
};


export type UserEntityEnrolledCoursesArgs = {
  course_status: InputMaybe<ECourseProgressStatus>;
  end_date: InputMaybe<Scalars['DateTime']['input']>;
  limit: InputMaybe<Scalars['Float']['input']>;
  page: InputMaybe<Scalars['Float']['input']>;
  start_date: InputMaybe<Scalars['DateTime']['input']>;
};

export type UserExamSnapshotEntity = {
  answers_snapshot: Maybe<Scalars['String']['output']>;
  created_at: Scalars['DateTime']['output'];
  exam_id: Scalars['Float']['output'];
  exam_snapshot: Scalars['String']['output'];
  grade: Scalars['Float']['output'];
  id: Scalars['Float']['output'];
  is_finished: Scalars['Boolean']['output'];
  updated_at: Scalars['DateTime']['output'];
  user_id: Scalars['Float']['output'];
};

export type UserLectureNoteEntity = {
  created_at: Scalars['DateTime']['output'];
  lecture: CourseChapterContentEntity;
  lecture_id: Scalars['Float']['output'];
  note: Scalars['String']['output'];
  updated_at: Scalars['DateTime']['output'];
  user: UserEntity;
  user_id: Scalars['Float']['output'];
};

export type UserMetadataEntity = {
  activities_completed: Maybe<Scalars['Float']['output']>;
  b2b_confirmation_account_token: Maybe<Scalars['String']['output']>;
  company: Maybe<CompanyEntity>;
  company_id: Maybe<Scalars['Float']['output']>;
  company_squad: Maybe<CompanySquadEntity>;
  has_forced_password_change: Scalars['Boolean']['output'];
  last_activity_completed_at: Maybe<Scalars['DateTime']['output']>;
  squad_id: Maybe<Scalars['Float']['output']>;
  user: Maybe<UserEntity>;
  user_id: Scalars['Float']['output'];
};

export type UsersCourseCertificationPivotEntity = {
  course: CourseEntity;
  course_id: Scalars['Float']['output'];
  created_at: Scalars['DateTime']['output'];
  file: FileEntity;
  file_id: Scalars['Float']['output'];
  file_type: EUserCourseCertificationFileType;
  has_to_reissue: Scalars['Boolean']['output'];
  parameters: Maybe<Scalars['String']['output']>;
  user_id: Scalars['Float']['output'];
  uuid: Scalars['String']['output'];
};

export type ViewCourseActivity = {
  activities_count: Scalars['Float']['output'];
};

export type ViewPaymentReport = {
  cost_center: Maybe<Scalars['String']['output']>;
  course_title: Maybe<Scalars['String']['output']>;
  cpf: Scalars['String']['output'];
  discount_type: Maybe<EPaymentDiscountType>;
  discount_value: Scalars['Float']['output'];
  email: Scalars['String']['output'];
  enrollment_status: EEnrollmentStatus;
  external_payment_id: Maybe<Scalars['String']['output']>;
  foreign_document: Maybe<Scalars['String']['output']>;
  is_blocked: Scalars['Boolean']['output'];
  is_hidden: Scalars['Boolean']['output'];
  name: Scalars['String']['output'];
  payed_installments: Scalars['Float']['output'];
  payment_agent: Maybe<EPaymentAgents>;
  payment_charge_id: Maybe<Scalars['String']['output']>;
  payment_coupon: Maybe<Scalars['String']['output']>;
  payment_date: Scalars['DateTime']['output'];
  payment_id: Scalars['Float']['output'];
  payment_method: Maybe<EPaymentMethod>;
  payment_our_number: Maybe<Scalars['String']['output']>;
  payment_plan: Maybe<Scalars['String']['output']>;
  payment_tid: Maybe<Scalars['String']['output']>;
  payment_type: Maybe<EPaymentType>;
  phone_number: Scalars['String']['output'];
  total_payed_value: Scalars['Float']['output'];
  user_id: Scalars['Float']['output'];
  user_status: Scalars['Boolean']['output'];
};

export type GetEnrollmentsByCompanyIdQueryVariables = Exact<{
  criteria: Scalars['String']['input'];
}>;


export type GetEnrollmentsByCompanyIdQuery = { company: { enrollments: Array<{ enrollment_id: string }> | null } };

export type GetLicenseActivesLmsQueryVariables = Exact<{ [key: string]: never; }>;


export type GetLicenseActivesLmsQuery = { userEnrollments: Array<{ id: string, type: EEnrollmentType }> };

export type GetUserProfileQueryVariables = Exact<{ [key: string]: never; }>;


export type GetUserProfileQuery = { profile: { payment_plan: { slug: string } | null } };

export type CreateStudyPlanMutationVariables = Exact<{
  name: Scalars['String']['input'];
  description: Scalars['String']['input'];
  courses: Array<StudyPlanCoursePivot> | StudyPlanCoursePivot;
  company_id: Scalars['Float']['input'];
  squad_ids: InputMaybe<Array<Scalars['Float']['input']> | Scalars['Float']['input']>;
  user_ids: InputMaybe<Array<Scalars['Float']['input']> | Scalars['Float']['input']>;
  is_pdi: InputMaybe<Scalars['Boolean']['input']>;
  is_for_all_users: InputMaybe<Scalars['Boolean']['input']>;
  is_for_all_squads: InputMaybe<Scalars['Boolean']['input']>;
  is_for_all_courses: InputMaybe<Scalars['Boolean']['input']>;
}>;


export type CreateStudyPlanMutation = { createStudyPlan: { id: number } };

export type DeleteStudyPlanMutationVariables = Exact<{
  id: Scalars['Float']['input'];
}>;


export type DeleteStudyPlanMutation = { deleteStudyPlan: boolean };

export type GetStudyPlansQueryVariables = Exact<{
  user_id: InputMaybe<Scalars['Float']['input']>;
  name: InputMaybe<Scalars['String']['input']>;
  company_id: Scalars['Float']['input'];
  limit: InputMaybe<Scalars['Float']['input']>;
  page: InputMaybe<Scalars['Float']['input']>;
  onlyPDI: InputMaybe<Scalars['Boolean']['input']>;
  status: InputMaybe<Scalars['Boolean']['input']>;
  end_date: InputMaybe<Scalars['DateTime']['input']>;
}>;


export type GetStudyPlansQuery = { studyPlans: { total: number, perPage: number, data: Array<{ ai_generated: boolean | null, end_date: any | null, status: boolean | null, id: number, name: string, coursesCount: number, squadsCount: number, usersCount: number, users_completed_count: number | null, courses_pivot: Array<{ course_id: number, course: { id: string, title: string } | null }> | null }> } };

export type UpdateCompanySquadMutationVariables = Exact<{
  id: Scalars['Float']['input'];
  title: Scalars['String']['input'];
  company_id: Scalars['Float']['input'];
  user_ids: InputMaybe<Array<Scalars['Float']['input']> | Scalars['Float']['input']>;
}>;


export type UpdateCompanySquadMutation = { updateCompanySquad: { id: number } };

export type DeleteCompanySquadMutationVariables = Exact<{
  id: Scalars['Int']['input'];
}>;


export type DeleteCompanySquadMutation = { deleteCompanySquad: boolean };

export type GetTeamByIdQueryVariables = Exact<{
  id: Scalars['Int']['input'];
  page: InputMaybe<Scalars['Int']['input']>;
  limit: InputMaybe<Scalars['Int']['input']>;
}>;


export type GetTeamByIdQuery = { companySquad: { id: number, title: string, users: { total: number, perPage: number, data: Array<{ id: number, name: string, email: string, updated_at: any }> } | null } };

export type GetTeamsQueryVariables = Exact<{
  limit: InputMaybe<Scalars['Float']['input']>;
  page: InputMaybe<Scalars['Float']['input']>;
  company_id: InputMaybe<Scalars['Float']['input']>;
  title: InputMaybe<Scalars['String']['input']>;
}>;


export type GetTeamsQuery = { companySquads: { total: number, perPage: number, data: Array<{ id: number, title: string, users_count: number, updated_at: any }> } };

export type GetB2BUsersQueryVariables = Exact<{
  all: Scalars['Boolean']['input'];
  page: Scalars['Float']['input'];
  limit: Scalars['Float']['input'];
  orderBy: EListOrderBy;
  order: EListOrder;
  company_id: Scalars['Float']['input'];
  enrollment_id: Scalars['Float']['input'];
  q: InputMaybe<Scalars['String']['input']>;
  hasSquad: InputMaybe<Scalars['Boolean']['input']>;
}>;


export type GetB2BUsersQuery = { users: { total: number, perPage: number, data: Array<{ id: number, name: string, email: string, last_login: any | null, enrollments_pivot: Array<{ status: EEnrollmentStatus }> | null, roles: Array<{ id: number, name: string, slug: string }> | null, metadata: { last_activity_completed_at: any | null, activities_completed: number | null, squad_id: number | null, company_squad: { id: number, title: string } | null } | null }> } };

export type CreateCompanySquadMutationVariables = Exact<{
  title: Scalars['String']['input'];
  company_id: Scalars['Float']['input'];
  user_ids: InputMaybe<Array<Scalars['Float']['input']> | Scalars['Float']['input']>;
}>;


export type CreateCompanySquadMutation = { createCompanySquad: { id: number } };


export const GetEnrollmentsByCompanyIdDocument = {"kind":"Document","definitions":[{"kind":"OperationDefinition","operation":"query","name":{"kind":"Name","value":"getEnrollmentsByCompanyId"},"variableDefinitions":[{"kind":"VariableDefinition","variable":{"kind":"Variable","name":{"kind":"Name","value":"criteria"}},"type":{"kind":"NonNullType","type":{"kind":"NamedType","name":{"kind":"Name","value":"String"}}}}],"selectionSet":{"kind":"SelectionSet","selections":[{"kind":"Field","name":{"kind":"Name","value":"company"},"arguments":[{"kind":"Argument","name":{"kind":"Name","value":"criteria"},"value":{"kind":"Variable","name":{"kind":"Name","value":"criteria"}}}],"selectionSet":{"kind":"SelectionSet","selections":[{"kind":"Field","name":{"kind":"Name","value":"enrollments"},"selectionSet":{"kind":"SelectionSet","selections":[{"kind":"Field","name":{"kind":"Name","value":"enrollment_id"}}]}}]}}]}}]} as unknown as DocumentNode<GetEnrollmentsByCompanyIdQuery, GetEnrollmentsByCompanyIdQueryVariables>;
export const GetLicenseActivesLmsDocument = {"kind":"Document","definitions":[{"kind":"OperationDefinition","operation":"query","name":{"kind":"Name","value":"getLicenseActivesLMS"},"selectionSet":{"kind":"SelectionSet","selections":[{"kind":"Field","name":{"kind":"Name","value":"userEnrollments"},"selectionSet":{"kind":"SelectionSet","selections":[{"kind":"Field","name":{"kind":"Name","value":"id"}},{"kind":"Field","name":{"kind":"Name","value":"type"}}]}}]}}]} as unknown as DocumentNode<GetLicenseActivesLmsQuery, GetLicenseActivesLmsQueryVariables>;
export const GetUserProfileDocument = {"kind":"Document","definitions":[{"kind":"OperationDefinition","operation":"query","name":{"kind":"Name","value":"getUserProfile"},"selectionSet":{"kind":"SelectionSet","selections":[{"kind":"Field","name":{"kind":"Name","value":"profile"},"selectionSet":{"kind":"SelectionSet","selections":[{"kind":"Field","name":{"kind":"Name","value":"payment_plan"},"selectionSet":{"kind":"SelectionSet","selections":[{"kind":"Field","name":{"kind":"Name","value":"slug"}}]}}]}}]}}]} as unknown as DocumentNode<GetUserProfileQuery, GetUserProfileQueryVariables>;
export const CreateStudyPlanDocument = {"kind":"Document","definitions":[{"kind":"OperationDefinition","operation":"mutation","name":{"kind":"Name","value":"CreateStudyPlan"},"variableDefinitions":[{"kind":"VariableDefinition","variable":{"kind":"Variable","name":{"kind":"Name","value":"name"}},"type":{"kind":"NonNullType","type":{"kind":"NamedType","name":{"kind":"Name","value":"String"}}}},{"kind":"VariableDefinition","variable":{"kind":"Variable","name":{"kind":"Name","value":"description"}},"type":{"kind":"NonNullType","type":{"kind":"NamedType","name":{"kind":"Name","value":"String"}}}},{"kind":"VariableDefinition","variable":{"kind":"Variable","name":{"kind":"Name","value":"courses"}},"type":{"kind":"NonNullType","type":{"kind":"ListType","type":{"kind":"NonNullType","type":{"kind":"NamedType","name":{"kind":"Name","value":"StudyPlanCoursePivot"}}}}}},{"kind":"VariableDefinition","variable":{"kind":"Variable","name":{"kind":"Name","value":"company_id"}},"type":{"kind":"NonNullType","type":{"kind":"NamedType","name":{"kind":"Name","value":"Float"}}}},{"kind":"VariableDefinition","variable":{"kind":"Variable","name":{"kind":"Name","value":"squad_ids"}},"type":{"kind":"ListType","type":{"kind":"NonNullType","type":{"kind":"NamedType","name":{"kind":"Name","value":"Float"}}}}},{"kind":"VariableDefinition","variable":{"kind":"Variable","name":{"kind":"Name","value":"user_ids"}},"type":{"kind":"ListType","type":{"kind":"NonNullType","type":{"kind":"NamedType","name":{"kind":"Name","value":"Float"}}}}},{"kind":"VariableDefinition","variable":{"kind":"Variable","name":{"kind":"Name","value":"is_pdi"}},"type":{"kind":"NamedType","name":{"kind":"Name","value":"Boolean"}}},{"kind":"VariableDefinition","variable":{"kind":"Variable","name":{"kind":"Name","value":"is_for_all_users"}},"type":{"kind":"NamedType","name":{"kind":"Name","value":"Boolean"}}},{"kind":"VariableDefinition","variable":{"kind":"Variable","name":{"kind":"Name","value":"is_for_all_squads"}},"type":{"kind":"NamedType","name":{"kind":"Name","value":"Boolean"}}},{"kind":"VariableDefinition","variable":{"kind":"Variable","name":{"kind":"Name","value":"is_for_all_courses"}},"type":{"kind":"NamedType","name":{"kind":"Name","value":"Boolean"}}}],"selectionSet":{"kind":"SelectionSet","selections":[{"kind":"Field","name":{"kind":"Name","value":"createStudyPlan"},"arguments":[{"kind":"Argument","name":{"kind":"Name","value":"data"},"value":{"kind":"ObjectValue","fields":[{"kind":"ObjectField","name":{"kind":"Name","value":"name"},"value":{"kind":"Variable","name":{"kind":"Name","value":"name"}}},{"kind":"ObjectField","name":{"kind":"Name","value":"description"},"value":{"kind":"Variable","name":{"kind":"Name","value":"description"}}},{"kind":"ObjectField","name":{"kind":"Name","value":"courses"},"value":{"kind":"Variable","name":{"kind":"Name","value":"courses"}}},{"kind":"ObjectField","name":{"kind":"Name","value":"company_id"},"value":{"kind":"Variable","name":{"kind":"Name","value":"company_id"}}},{"kind":"ObjectField","name":{"kind":"Name","value":"squad_ids"},"value":{"kind":"Variable","name":{"kind":"Name","value":"squad_ids"}}},{"kind":"ObjectField","name":{"kind":"Name","value":"user_ids"},"value":{"kind":"Variable","name":{"kind":"Name","value":"user_ids"}}},{"kind":"ObjectField","name":{"kind":"Name","value":"is_pdi"},"value":{"kind":"Variable","name":{"kind":"Name","value":"is_pdi"}}},{"kind":"ObjectField","name":{"kind":"Name","value":"is_for_all_users"},"value":{"kind":"Variable","name":{"kind":"Name","value":"is_for_all_users"}}},{"kind":"ObjectField","name":{"kind":"Name","value":"is_for_all_squads"},"value":{"kind":"Variable","name":{"kind":"Name","value":"is_for_all_squads"}}},{"kind":"ObjectField","name":{"kind":"Name","value":"is_for_all_courses"},"value":{"kind":"Variable","name":{"kind":"Name","value":"is_for_all_courses"}}}]}}],"selectionSet":{"kind":"SelectionSet","selections":[{"kind":"Field","name":{"kind":"Name","value":"id"}}]}}]}}]} as unknown as DocumentNode<CreateStudyPlanMutation, CreateStudyPlanMutationVariables>;
export const DeleteStudyPlanDocument = {"kind":"Document","definitions":[{"kind":"OperationDefinition","operation":"mutation","name":{"kind":"Name","value":"deleteStudyPlan"},"variableDefinitions":[{"kind":"VariableDefinition","variable":{"kind":"Variable","name":{"kind":"Name","value":"id"}},"type":{"kind":"NonNullType","type":{"kind":"NamedType","name":{"kind":"Name","value":"Float"}}}}],"selectionSet":{"kind":"SelectionSet","selections":[{"kind":"Field","name":{"kind":"Name","value":"deleteStudyPlan"},"arguments":[{"kind":"Argument","name":{"kind":"Name","value":"id"},"value":{"kind":"Variable","name":{"kind":"Name","value":"id"}}}]}]}}]} as unknown as DocumentNode<DeleteStudyPlanMutation, DeleteStudyPlanMutationVariables>;
export const GetStudyPlansDocument = {"kind":"Document","definitions":[{"kind":"OperationDefinition","operation":"query","name":{"kind":"Name","value":"getStudyPlans"},"variableDefinitions":[{"kind":"VariableDefinition","variable":{"kind":"Variable","name":{"kind":"Name","value":"user_id"}},"type":{"kind":"NamedType","name":{"kind":"Name","value":"Float"}}},{"kind":"VariableDefinition","variable":{"kind":"Variable","name":{"kind":"Name","value":"name"}},"type":{"kind":"NamedType","name":{"kind":"Name","value":"String"}}},{"kind":"VariableDefinition","variable":{"kind":"Variable","name":{"kind":"Name","value":"company_id"}},"type":{"kind":"NonNullType","type":{"kind":"NamedType","name":{"kind":"Name","value":"Float"}}}},{"kind":"VariableDefinition","variable":{"kind":"Variable","name":{"kind":"Name","value":"limit"}},"type":{"kind":"NamedType","name":{"kind":"Name","value":"Float"}}},{"kind":"VariableDefinition","variable":{"kind":"Variable","name":{"kind":"Name","value":"page"}},"type":{"kind":"NamedType","name":{"kind":"Name","value":"Float"}}},{"kind":"VariableDefinition","variable":{"kind":"Variable","name":{"kind":"Name","value":"onlyPDI"}},"type":{"kind":"NamedType","name":{"kind":"Name","value":"Boolean"}}},{"kind":"VariableDefinition","variable":{"kind":"Variable","name":{"kind":"Name","value":"status"}},"type":{"kind":"NamedType","name":{"kind":"Name","value":"Boolean"}}},{"kind":"VariableDefinition","variable":{"kind":"Variable","name":{"kind":"Name","value":"end_date"}},"type":{"kind":"NamedType","name":{"kind":"Name","value":"DateTime"}}}],"selectionSet":{"kind":"SelectionSet","selections":[{"kind":"Field","name":{"kind":"Name","value":"studyPlans"},"arguments":[{"kind":"Argument","name":{"kind":"Name","value":"name"},"value":{"kind":"Variable","name":{"kind":"Name","value":"name"}}},{"kind":"Argument","name":{"kind":"Name","value":"company_id"},"value":{"kind":"Variable","name":{"kind":"Name","value":"company_id"}}},{"kind":"Argument","name":{"kind":"Name","value":"limit"},"value":{"kind":"Variable","name":{"kind":"Name","value":"limit"}}},{"kind":"Argument","name":{"kind":"Name","value":"page"},"value":{"kind":"Variable","name":{"kind":"Name","value":"page"}}},{"kind":"Argument","name":{"kind":"Name","value":"onlyPDI"},"value":{"kind":"Variable","name":{"kind":"Name","value":"onlyPDI"}}},{"kind":"Argument","name":{"kind":"Name","value":"user_id"},"value":{"kind":"Variable","name":{"kind":"Name","value":"user_id"}}},{"kind":"Argument","name":{"kind":"Name","value":"status"},"value":{"kind":"Variable","name":{"kind":"Name","value":"status"}}},{"kind":"Argument","name":{"kind":"Name","value":"end_date"},"value":{"kind":"Variable","name":{"kind":"Name","value":"end_date"}}}],"selectionSet":{"kind":"SelectionSet","selections":[{"kind":"Field","name":{"kind":"Name","value":"data"},"selectionSet":{"kind":"SelectionSet","selections":[{"kind":"Field","name":{"kind":"Name","value":"courses_pivot"},"selectionSet":{"kind":"SelectionSet","selections":[{"kind":"Field","name":{"kind":"Name","value":"course_id"}},{"kind":"Field","name":{"kind":"Name","value":"course"},"selectionSet":{"kind":"SelectionSet","selections":[{"kind":"Field","name":{"kind":"Name","value":"id"}},{"kind":"Field","name":{"kind":"Name","value":"title"}}]}}]}},{"kind":"Field","name":{"kind":"Name","value":"ai_generated"}},{"kind":"Field","name":{"kind":"Name","value":"end_date"}},{"kind":"Field","name":{"kind":"Name","value":"status"}},{"kind":"Field","name":{"kind":"Name","value":"id"}},{"kind":"Field","name":{"kind":"Name","value":"name"}},{"kind":"Field","name":{"kind":"Name","value":"coursesCount"}},{"kind":"Field","name":{"kind":"Name","value":"squadsCount"}},{"kind":"Field","name":{"kind":"Name","value":"usersCount"}},{"kind":"Field","name":{"kind":"Name","value":"users_completed_count"}}]}},{"kind":"Field","name":{"kind":"Name","value":"total"}},{"kind":"Field","name":{"kind":"Name","value":"perPage"}}]}}]}}]} as unknown as DocumentNode<GetStudyPlansQuery, GetStudyPlansQueryVariables>;
export const UpdateCompanySquadDocument = {"kind":"Document","definitions":[{"kind":"OperationDefinition","operation":"mutation","name":{"kind":"Name","value":"updateCompanySquad"},"variableDefinitions":[{"kind":"VariableDefinition","variable":{"kind":"Variable","name":{"kind":"Name","value":"id"}},"type":{"kind":"NonNullType","type":{"kind":"NamedType","name":{"kind":"Name","value":"Float"}}}},{"kind":"VariableDefinition","variable":{"kind":"Variable","name":{"kind":"Name","value":"title"}},"type":{"kind":"NonNullType","type":{"kind":"NamedType","name":{"kind":"Name","value":"String"}}}},{"kind":"VariableDefinition","variable":{"kind":"Variable","name":{"kind":"Name","value":"company_id"}},"type":{"kind":"NonNullType","type":{"kind":"NamedType","name":{"kind":"Name","value":"Float"}}}},{"kind":"VariableDefinition","variable":{"kind":"Variable","name":{"kind":"Name","value":"user_ids"}},"type":{"kind":"ListType","type":{"kind":"NonNullType","type":{"kind":"NamedType","name":{"kind":"Name","value":"Float"}}}}}],"selectionSet":{"kind":"SelectionSet","selections":[{"kind":"Field","name":{"kind":"Name","value":"updateCompanySquad"},"arguments":[{"kind":"Argument","name":{"kind":"Name","value":"data"},"value":{"kind":"ObjectValue","fields":[{"kind":"ObjectField","name":{"kind":"Name","value":"id"},"value":{"kind":"Variable","name":{"kind":"Name","value":"id"}}},{"kind":"ObjectField","name":{"kind":"Name","value":"title"},"value":{"kind":"Variable","name":{"kind":"Name","value":"title"}}},{"kind":"ObjectField","name":{"kind":"Name","value":"company_id"},"value":{"kind":"Variable","name":{"kind":"Name","value":"company_id"}}},{"kind":"ObjectField","name":{"kind":"Name","value":"user_ids"},"value":{"kind":"Variable","name":{"kind":"Name","value":"user_ids"}}}]}}],"selectionSet":{"kind":"SelectionSet","selections":[{"kind":"Field","name":{"kind":"Name","value":"id"}}]}}]}}]} as unknown as DocumentNode<UpdateCompanySquadMutation, UpdateCompanySquadMutationVariables>;
export const DeleteCompanySquadDocument = {"kind":"Document","definitions":[{"kind":"OperationDefinition","operation":"mutation","name":{"kind":"Name","value":"deleteCompanySquad"},"variableDefinitions":[{"kind":"VariableDefinition","variable":{"kind":"Variable","name":{"kind":"Name","value":"id"}},"type":{"kind":"NonNullType","type":{"kind":"NamedType","name":{"kind":"Name","value":"Int"}}}}],"selectionSet":{"kind":"SelectionSet","selections":[{"kind":"Field","name":{"kind":"Name","value":"deleteCompanySquad"},"arguments":[{"kind":"Argument","name":{"kind":"Name","value":"id"},"value":{"kind":"Variable","name":{"kind":"Name","value":"id"}}}]}]}}]} as unknown as DocumentNode<DeleteCompanySquadMutation, DeleteCompanySquadMutationVariables>;
export const GetTeamByIdDocument = {"kind":"Document","definitions":[{"kind":"OperationDefinition","operation":"query","name":{"kind":"Name","value":"getTeamById"},"variableDefinitions":[{"kind":"VariableDefinition","variable":{"kind":"Variable","name":{"kind":"Name","value":"id"}},"type":{"kind":"NonNullType","type":{"kind":"NamedType","name":{"kind":"Name","value":"Int"}}}},{"kind":"VariableDefinition","variable":{"kind":"Variable","name":{"kind":"Name","value":"page"}},"type":{"kind":"NamedType","name":{"kind":"Name","value":"Int"}}},{"kind":"VariableDefinition","variable":{"kind":"Variable","name":{"kind":"Name","value":"limit"}},"type":{"kind":"NamedType","name":{"kind":"Name","value":"Int"}}}],"selectionSet":{"kind":"SelectionSet","selections":[{"kind":"Field","name":{"kind":"Name","value":"companySquad"},"arguments":[{"kind":"Argument","name":{"kind":"Name","value":"id"},"value":{"kind":"Variable","name":{"kind":"Name","value":"id"}}}],"selectionSet":{"kind":"SelectionSet","selections":[{"kind":"Field","name":{"kind":"Name","value":"id"}},{"kind":"Field","name":{"kind":"Name","value":"title"}},{"kind":"Field","name":{"kind":"Name","value":"users"},"arguments":[{"kind":"Argument","name":{"kind":"Name","value":"limit"},"value":{"kind":"Variable","name":{"kind":"Name","value":"limit"}}},{"kind":"Argument","name":{"kind":"Name","value":"page"},"value":{"kind":"Variable","name":{"kind":"Name","value":"page"}}}],"selectionSet":{"kind":"SelectionSet","selections":[{"kind":"Field","name":{"kind":"Name","value":"total"}},{"kind":"Field","name":{"kind":"Name","value":"perPage"}},{"kind":"Field","name":{"kind":"Name","value":"data"},"selectionSet":{"kind":"SelectionSet","selections":[{"kind":"Field","name":{"kind":"Name","value":"id"}},{"kind":"Field","name":{"kind":"Name","value":"name"}},{"kind":"Field","name":{"kind":"Name","value":"email"}},{"kind":"Field","name":{"kind":"Name","value":"updated_at"}}]}}]}}]}}]}}]} as unknown as DocumentNode<GetTeamByIdQuery, GetTeamByIdQueryVariables>;
export const GetTeamsDocument = {"kind":"Document","definitions":[{"kind":"OperationDefinition","operation":"query","name":{"kind":"Name","value":"getTeams"},"variableDefinitions":[{"kind":"VariableDefinition","variable":{"kind":"Variable","name":{"kind":"Name","value":"limit"}},"type":{"kind":"NamedType","name":{"kind":"Name","value":"Float"}}},{"kind":"VariableDefinition","variable":{"kind":"Variable","name":{"kind":"Name","value":"page"}},"type":{"kind":"NamedType","name":{"kind":"Name","value":"Float"}}},{"kind":"VariableDefinition","variable":{"kind":"Variable","name":{"kind":"Name","value":"company_id"}},"type":{"kind":"NamedType","name":{"kind":"Name","value":"Float"}}},{"kind":"VariableDefinition","variable":{"kind":"Variable","name":{"kind":"Name","value":"title"}},"type":{"kind":"NamedType","name":{"kind":"Name","value":"String"}}}],"selectionSet":{"kind":"SelectionSet","selections":[{"kind":"Field","name":{"kind":"Name","value":"companySquads"},"arguments":[{"kind":"Argument","name":{"kind":"Name","value":"limit"},"value":{"kind":"Variable","name":{"kind":"Name","value":"limit"}}},{"kind":"Argument","name":{"kind":"Name","value":"page"},"value":{"kind":"Variable","name":{"kind":"Name","value":"page"}}},{"kind":"Argument","name":{"kind":"Name","value":"company_id"},"value":{"kind":"Variable","name":{"kind":"Name","value":"company_id"}}},{"kind":"Argument","name":{"kind":"Name","value":"title"},"value":{"kind":"Variable","name":{"kind":"Name","value":"title"}}}],"selectionSet":{"kind":"SelectionSet","selections":[{"kind":"Field","name":{"kind":"Name","value":"data"},"selectionSet":{"kind":"SelectionSet","selections":[{"kind":"Field","name":{"kind":"Name","value":"id"}},{"kind":"Field","name":{"kind":"Name","value":"title"}},{"kind":"Field","name":{"kind":"Name","value":"users_count"}},{"kind":"Field","name":{"kind":"Name","value":"updated_at"}}]}},{"kind":"Field","name":{"kind":"Name","value":"total"}},{"kind":"Field","name":{"kind":"Name","value":"perPage"}}]}}]}}]} as unknown as DocumentNode<GetTeamsQuery, GetTeamsQueryVariables>;
export const GetB2BUsersDocument = {"kind":"Document","definitions":[{"kind":"OperationDefinition","operation":"query","name":{"kind":"Name","value":"GetB2BUsers"},"variableDefinitions":[{"kind":"VariableDefinition","variable":{"kind":"Variable","name":{"kind":"Name","value":"all"}},"type":{"kind":"NonNullType","type":{"kind":"NamedType","name":{"kind":"Name","value":"Boolean"}}}},{"kind":"VariableDefinition","variable":{"kind":"Variable","name":{"kind":"Name","value":"page"}},"type":{"kind":"NonNullType","type":{"kind":"NamedType","name":{"kind":"Name","value":"Float"}}}},{"kind":"VariableDefinition","variable":{"kind":"Variable","name":{"kind":"Name","value":"limit"}},"type":{"kind":"NonNullType","type":{"kind":"NamedType","name":{"kind":"Name","value":"Float"}}}},{"kind":"VariableDefinition","variable":{"kind":"Variable","name":{"kind":"Name","value":"orderBy"}},"type":{"kind":"NonNullType","type":{"kind":"NamedType","name":{"kind":"Name","value":"EListOrderBy"}}}},{"kind":"VariableDefinition","variable":{"kind":"Variable","name":{"kind":"Name","value":"order"}},"type":{"kind":"NonNullType","type":{"kind":"NamedType","name":{"kind":"Name","value":"EListOrder"}}}},{"kind":"VariableDefinition","variable":{"kind":"Variable","name":{"kind":"Name","value":"company_id"}},"type":{"kind":"NonNullType","type":{"kind":"NamedType","name":{"kind":"Name","value":"Float"}}}},{"kind":"VariableDefinition","variable":{"kind":"Variable","name":{"kind":"Name","value":"enrollment_id"}},"type":{"kind":"NonNullType","type":{"kind":"NamedType","name":{"kind":"Name","value":"Float"}}}},{"kind":"VariableDefinition","variable":{"kind":"Variable","name":{"kind":"Name","value":"q"}},"type":{"kind":"NamedType","name":{"kind":"Name","value":"String"}}},{"kind":"VariableDefinition","variable":{"kind":"Variable","name":{"kind":"Name","value":"hasSquad"}},"type":{"kind":"NamedType","name":{"kind":"Name","value":"Boolean"}}}],"selectionSet":{"kind":"SelectionSet","selections":[{"kind":"Field","name":{"kind":"Name","value":"users"},"arguments":[{"kind":"Argument","name":{"kind":"Name","value":"all"},"value":{"kind":"Variable","name":{"kind":"Name","value":"all"}}},{"kind":"Argument","name":{"kind":"Name","value":"page"},"value":{"kind":"Variable","name":{"kind":"Name","value":"page"}}},{"kind":"Argument","name":{"kind":"Name","value":"limit"},"value":{"kind":"Variable","name":{"kind":"Name","value":"limit"}}},{"kind":"Argument","name":{"kind":"Name","value":"orderBy"},"value":{"kind":"Variable","name":{"kind":"Name","value":"orderBy"}}},{"kind":"Argument","name":{"kind":"Name","value":"order"},"value":{"kind":"Variable","name":{"kind":"Name","value":"order"}}},{"kind":"Argument","name":{"kind":"Name","value":"company_id"},"value":{"kind":"Variable","name":{"kind":"Name","value":"company_id"}}},{"kind":"Argument","name":{"kind":"Name","value":"enrollment_id"},"value":{"kind":"Variable","name":{"kind":"Name","value":"enrollment_id"}}},{"kind":"Argument","name":{"kind":"Name","value":"q"},"value":{"kind":"Variable","name":{"kind":"Name","value":"q"}}},{"kind":"Argument","name":{"kind":"Name","value":"hasSquad"},"value":{"kind":"Variable","name":{"kind":"Name","value":"hasSquad"}}}],"selectionSet":{"kind":"SelectionSet","selections":[{"kind":"Field","name":{"kind":"Name","value":"total"}},{"kind":"Field","name":{"kind":"Name","value":"perPage"}},{"kind":"Field","name":{"kind":"Name","value":"data"},"selectionSet":{"kind":"SelectionSet","selections":[{"kind":"Field","name":{"kind":"Name","value":"id"}},{"kind":"Field","name":{"kind":"Name","value":"name"}},{"kind":"Field","name":{"kind":"Name","value":"email"}},{"kind":"Field","name":{"kind":"Name","value":"last_login"}},{"kind":"Field","name":{"kind":"Name","value":"enrollments_pivot"},"selectionSet":{"kind":"SelectionSet","selections":[{"kind":"Field","name":{"kind":"Name","value":"status"}}]}},{"kind":"Field","name":{"kind":"Name","value":"roles"},"selectionSet":{"kind":"SelectionSet","selections":[{"kind":"Field","name":{"kind":"Name","value":"id"}},{"kind":"Field","name":{"kind":"Name","value":"name"}},{"kind":"Field","name":{"kind":"Name","value":"slug"}}]}},{"kind":"Field","name":{"kind":"Name","value":"metadata"},"selectionSet":{"kind":"SelectionSet","selections":[{"kind":"Field","name":{"kind":"Name","value":"last_activity_completed_at"}},{"kind":"Field","name":{"kind":"Name","value":"activities_completed"}},{"kind":"Field","name":{"kind":"Name","value":"squad_id"}},{"kind":"Field","name":{"kind":"Name","value":"company_squad"},"selectionSet":{"kind":"SelectionSet","selections":[{"kind":"Field","name":{"kind":"Name","value":"id"}},{"kind":"Field","name":{"kind":"Name","value":"title"}}]}}]}}]}}]}}]}}]} as unknown as DocumentNode<GetB2BUsersQuery, GetB2BUsersQueryVariables>;
export const CreateCompanySquadDocument = {"kind":"Document","definitions":[{"kind":"OperationDefinition","operation":"mutation","name":{"kind":"Name","value":"CreateCompanySquad"},"variableDefinitions":[{"kind":"VariableDefinition","variable":{"kind":"Variable","name":{"kind":"Name","value":"title"}},"type":{"kind":"NonNullType","type":{"kind":"NamedType","name":{"kind":"Name","value":"String"}}}},{"kind":"VariableDefinition","variable":{"kind":"Variable","name":{"kind":"Name","value":"company_id"}},"type":{"kind":"NonNullType","type":{"kind":"NamedType","name":{"kind":"Name","value":"Float"}}}},{"kind":"VariableDefinition","variable":{"kind":"Variable","name":{"kind":"Name","value":"user_ids"}},"type":{"kind":"ListType","type":{"kind":"NonNullType","type":{"kind":"NamedType","name":{"kind":"Name","value":"Float"}}}}}],"selectionSet":{"kind":"SelectionSet","selections":[{"kind":"Field","name":{"kind":"Name","value":"createCompanySquad"},"arguments":[{"kind":"Argument","name":{"kind":"Name","value":"data"},"value":{"kind":"ObjectValue","fields":[{"kind":"ObjectField","name":{"kind":"Name","value":"title"},"value":{"kind":"Variable","name":{"kind":"Name","value":"title"}}},{"kind":"ObjectField","name":{"kind":"Name","value":"company_id"},"value":{"kind":"Variable","name":{"kind":"Name","value":"company_id"}}},{"kind":"ObjectField","name":{"kind":"Name","value":"user_ids"},"value":{"kind":"Variable","name":{"kind":"Name","value":"user_ids"}}}]}}],"selectionSet":{"kind":"SelectionSet","selections":[{"kind":"Field","name":{"kind":"Name","value":"id"}}]}}]}}]} as unknown as DocumentNode<CreateCompanySquadMutation, CreateCompanySquadMutationVariables>;